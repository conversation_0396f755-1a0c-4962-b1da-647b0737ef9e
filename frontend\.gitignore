# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
out/
dist/

# production
/build

# misc
.DS_Store
Thumbs.db
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env
.env.*
!.env.example

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# OS files
*.tgz
*.log
*.pid
*.seed
*.pid.lock

# Editor/IDE
.vscode/
.idea/
*.swp
*.swo
*.sublime*

# Docker
*.local
# Dockerfile*
.dockerignore

# CI/CD
*.env.ci

# Output from npm pack
*.tgz

# Ignore custom scripts
# run.sh

# Ignore lock files (optional, uncomment if you want to ignore lock files)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml
