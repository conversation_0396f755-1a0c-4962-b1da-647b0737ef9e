{"name": "frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"framer-motion": "^11.18.2", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "next": "15.3.2", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.18", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5"}}