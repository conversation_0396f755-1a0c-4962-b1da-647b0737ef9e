## HỌC VIỆN KỸ THUẬT MẬT MÃ

## KHOA AN TOÀN THÔNG TIN

![Ảnh đã được loại bỏ để giảm kích thước file]

## BÁO CÁO MÔN HỌC

## PHÂN TÍCH VÀ THIẾT KẾ AN TOÀN MẠNG MÁY TÍNH

## ĐỀ TÀI:

## XÂY DỰNG MẠNG VPN CHO HỆ THỐNG MẠNG

Sinh viên thực hiện: Trịnh Thị Dung AT150209 Nguyễn Thùy Dương AT150211 <PERSON>uyễn Minh Hằng AT150216 Đỗ Duy Hưng AT150225 Nhóm 13 Giảng viên hướng dẫn: GV. TRẦN NGHI PHÚ

## TÓM TẮT

Mạng riêng ảo VPN (Vitrual Private Network) là một mạng riêng rẽ được sử dụng một mạng chung (thường là Internet) để kết nối cùng với các mạng riêng lẻ (các site) hay nhiều người sử dụng từ xa. Thay cho việc sử dụng bởi một kết nối thực, chuyên dụng như đường Leased Line, mỗi VPN sử dụng các kết nối ảo được dẫn qua đường Internet từ mạng riêng của công ty tới các mạng riêng lẻ của các nhân viên từ xa.

Một ứng dụng điển hình của VPN là cung cấp một kênh an toàn từ đầu mạng giúp cho những văn phòng chi nhánh hay những văn phòng ở xa nhau hay những nhân viên làm việc từ xa có thể dùng mạng Internet truy cập tài nguyên ở cơ quan, doanh nghiệp chính một cách bảo mật và đầy đủ tính năng như đang sử dụng máy tính cục bộ tại ngay cơ quan, doanh nghiệp đó.

Những thiết bị ở đầu mạng hỗ trợ cho VPN là Switch, Router, Firewall. Những thiết bị này có thể được quản trị bởi công ty hoặc các nhà mạng cung cấp dịch vụ như ISP.

## Về ưu điểm:

- -Tính bảo mật: VPN mã hóa tất cả dữ liệu trên đường hầm.
- -Tiết kiệm chi phí: Sự xuất hiện của VPN đã làm cho những cuộc quay số đường dài tốn kém hay đường dây thuê bao không còn cần thiết nữa đối với những tổ chức sử dụng VPN đóng gói dữ liệu một cách an toàn qua mạng Internet.
- - Chủ động về thời gian: Những tổ chức có văn phòng chi nhánh hay những văn phòng ở xa nhau hay những nhân viên làm việc từ xa có thể truy cập dữ liệu của cơ quan, doanh nghiệp chính từ bất kỳ địa điểm nào trên thế giới mà không phải tốn kém nhiều, chỉ cần có kết nối mạng Internet thông qua nhà cung cấp dịch vụ địa phương.

## MỤC LỤC

| 1..................................................................................................................      | 3                                                                              |
|--------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------|
| GIỚI THIỆU MÔHÌNHMẠNGRIÊNGẢOVPN.............................................3                                            |                                                                                |
| I. TỔNG QUANVỀVPN......................................................................................                  | 3                                                                              |
| I.1. Định nghĩa, chức năng, và ưu điểm của VPN...............................................                            | 3                                                                              |
| I.1.1. Khái niệm cơ bản về VPN.....................................................................                      | 3                                                                              |
| I.1.2. Chức năng của VPN..............................................................................                   | 4                                                                              |
| I.1.3. Ưu điểm................................................................................................           | 5                                                                              |
| I.1.4. Các yêu cầu cơ bản đối với một giải pháp VPN....................................                                  | 7                                                                              |
| I.2. Đường hầm và mã hóa..................................................................................               | 8                                                                              |
| II. CÁC KIỂU VPN...............................................................................................          | 8                                                                              |
| II.1. Các VPN truy cập (Remote Access VPNs).................................................                             | 9                                                                              |
| II.2. Các VPN nội bộ (Intranet VPNs):.............................................................12                     |                                                                                |
| II.3. Các VPN mở rộng (Extranet VPNs):........................................................14                         |                                                                                |
| Chương 2................................................................................................................ | 17                                                                             |
| GIAO THỨC ĐƯỜNG                                                                                                          | HẦMVPN......................................................................17 |
| I. GIỚI THIỆU CÁC GIAO THỨC ĐƯỜNG HẦM...........................................17                                       |                                                                                |
| II. GIAO THỨC ĐƯỜNG HẦMĐIỂMTỚI ĐIỂM (PPTP)...............................18                                              |                                                                                |
| II.1. Nguyên tắc hoạt động của PPTP...............................................................18                     |                                                                                |
| II.2. Nguyên tắc kết nối điều khiển đường hầm theo giao thức PPTP..............20                                        |                                                                                |
| II.3. Nguyên lý đóng gói dữ liệu đường hầm PPTP..........................................20                              |                                                                                |
| II.4. Nguyên tắc thực hiện gói tin dữ liệu tại đầu cuối đường hầm PPTP.........22                                        |                                                                                |
| II.5. Triển khai VPN dựa trên PPTP.................................................................22                    |                                                                                |
| II.5.1. Máy chủ PPTP...................................................................................23                |                                                                                |
| II.5.2. Phần mềm Client PPTP......................................................................24                     |                                                                                |
| II.5.3. Máy chủ truy nhập mạng...................................................................24                      |                                                                                |
| II.6. Một số ưu nhược điểm và khả năng ứng dụng của PPTP..........................24                                     |                                                                                |

## DANH MỤC CÁC TỪ VIẾT TẮT

| ATM:Asynchronous Transfer Mode (Chế độ chuyển tiếp bất đồng bộ) Challenge Handshake Authentication Protocol(Giao thức thử thách bắt tay) Confidentiality, Integrity, and Availability (Tính bảo mật, Tính toàn vẹn và Tính sẵn có)   |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| CHAP: CIA:                                                                                                                                                                                                                           |
| DOS: Denial Of Service (Tấn công từ chối dịch vụ)                                                                                                                                                                                    |
| EAP: Extensible Authorized Protocol (Giao thức xác thực mở rộng)                                                                                                                                                                     |
| FCS: Frame Check Sequence (Chuỗi kiểm tra khung)                                                                                                                                                                                     |
| GRE: Generic Routing Protocol (Giao thức định tuyến chung)                                                                                                                                                                           |
| IETF: Internet Engineering Task Force (Là một tổ chức tiêu chuẩn mở, phát triển                                                                                                                                                      |
| và xúc tiến các tiêu chuẩn về Internet)                                                                                                                                                                                              |
| LAN: Local Area Network (Mạng cục bộ)                                                                                                                                                                                                |
| LCP: Link Control Protocol (Giao thức điểm khiển đường truyền)                                                                                                                                                                       |
| L2F: Layer 2 Forwarding (Giao thức chuyển tiếp lớp 2)                                                                                                                                                                                |
| L2TP: Layer 2 Tunneling Protocol (Giao thức đường hầm lớp thứ 2)                                                                                                                                                                     |
| IP: Internet Protocol (Giao thức Internet)                                                                                                                                                                                           |
| IPSec: Internet Protocol security ( Bảo mật giao thức Internet)                                                                                                                                                                      |
| ISDN: Integrated Services Digital Network ( Mạng kỹ thuật số toàn vẹn về dịch vụ)                                                                                                                                                    |
| IPX: Internet Protocol Exchange (Trao đổi giao thức Internet)                                                                                                                                                                        |
| của Microsoft)                                                                                                                                                                                                                       |
| NDIS: Network Driver Interface Specification ( Đặc tả giao diện Network Driver)                                                                                                                                                      |
| NetBEUI: NETBIOS Extended User Inteface (Giao diện người dùng mở rộng                                                                                                                                                                |
| OC3: Optical Carrier 3 ( Tiêu chuẩn về đơn vị chuyển băng thông)                                                                                                                                                                     |
| OSI: Open System Interconnection (Chuẩn giao tiếp quốc tế về mô hình mạng)                                                                                                                                                           |
| NETBIOS)                                                                                                                                                                                                                             |
| NAS: Network Access Server (Server được truy cập từ mạng)                                                                                                                                                                            |
| ISP: Internet Service Provides (Nhà cung cấp dịch vụ Internet)                                                                                                                                                                       |
| MPPE:Microsoft Point-to-Point Encryption (Mã hóa Điểm tới                                                                                                                                                                            |
| Điểm                                                                                                                                                                                                                                 |
| NDIS-WAN: Network Driver Inteface Wide Area Network.                                                                                                                                                                                 |

- PAP: Password Authentication Protocol (Giao thức xác thực mật khẩu mở rộng)
- POP: Point of Presence (Điểm mà tại đó hai hay nhiều thiết bị kết nối internet giao tiếp với nhau)
- PPP: Point-to-Point Protocol (Giao thức Điểm tới Điểm)
- PPTP: Point-to-Point Tunneling Protocol (Giao thức đường hầm Điểm tới Điểm)
- PVC: Permanent Virtual Circuit (Mạch ảo vĩnh viễn)
- QoS: Quality of Service (cách thức điều khiển mức độ ưu tiên traffic của hệ thống mạng)

RAS: Remote Access Server (Máy chủ truy cập từ xa)

- TCP: Transmission Control Protocol ( Giao thức chuyển giao điều khiển)
- TCP 1723: Chuẩn TCP 1723

TCP/IP: một bộ các giao thức truyền thông cài đặt chồng giao thức mà Internet và hầu hết các mạng máy tính thương mại đang chạy trên đó. Đặt tên theo 2 giao thức TCP và IP.

VPN: Vitrual Private Network (Mạng riêng ảo)

- WAN: Wide Area Network (Mạng diện rộng)

## PHẦN MỞ ĐẦU

## Lý do chọn đề tài

Với sự phát triển hàng ngày của khoa học công nghệ thông tin, công nghệ mạng máy tính và đặc biệt không thể không nhắc đến là hệ thống thông tin toàn cầu -mạng Internet. Mạng Internet ngày càng phát triển đa dạng và phong phú, các dịch vụ trên mạng Internet đã xâm nhập vào hầu hết các lĩnh vực trong đời sống xã hội. Các thông tin trên Internet cũng đa dạng cả về nội dung lẫn hình thức, trong đó có rất nhiều thông tin cần được bảo mật cao bởi tính kinh tế, tính chính xác và sự tin cậy của nó.

Bên cạnh đó, những dịch vụ mạng ngày càng có giá trị, yêu cầu phải đảm bảo tính ổn định và an toàn cao. Tuy nhiên, các hình thức phá hoại cũng trở nên tinh vi và phức tạp hơn, do đó đối với mỗi hệ thống, nhiệm vụ bảo mật đặt ra cho người quản trị là hết sức quan trọng và cần thiết.

Xuất phát từ những thực tế nêu trên, hiện nay trên thế giới đã xuất hiện rất nhiều công nghệ tiên tiến liên quan đến bảo mật hệ thống và mạng máy tính, việc tìm hiểu và nắm bắt được những công nghệ này là điều hết sức thiết yếu và cần thiết.

Chính vì vậy, thông qua việc nghiên cứu và tìm hiều một cách tổng quan về bảo mật hệ thống và nhiều công nghệ bảo mật tiên tiến hiện nay, cụ thể nhóm chúng em xin tìm hiểu về một công nghệ, đó là công nghệ mạng riêng ảo (VPN - Vitrual Private Network). Trong Báo cáo này, chúng em xin góp một phần vào việc tìm hiểu thêm và nắm bắt rõ về kỹ thuật VPN trong cơ quan, doanh nghiệp, nhà trường… nhằm phục vụ cho việc học tập và nghiên cứu.

Bảo mật hệ thống, bảo mật mạng máy tính hay chỉ nói riêng kỹ thuật VPN là một vấn đề vô cùng rộng lớn, đồng thời do kinh nghiệm cùng với kiến thức còn hạn chế, nội dung Báo cáo chắc chắn sẽ còn nhiều sai sót và chưa đầy đủ, hy vọng thầy cô cùng các bạn sinh viên sẽ đóng góp thêm ý kiến bổ sung nhằm giúp nhóm chúng em hoàn thiện Báo cáo được chính xác và hữu ích hơn.

Chúng em xin chân thành cảm ơn!

## Chương 1

## GIỚI THIỆU MÔ HÌNH MẠNG RIÊNG ẢO VPN

## I. TỔNG QUAN VỀ VPN

## I.1. Định nghĩa, chức năng, và ưu điểm của VPN

## I.1.1. Khái niệm cơ bản về VPN

Phương án truyền thông nhanh, an toàn và tin cậy đang trở thành mối quan tâm của nhiều doanh nghiệp, đặc biệt là các doanh nghiệp có các địa điểm phân tán về mặt địa lý. Nếu như trước đây giải pháp thông thường là thuê các đường truyền riêng (Leased Lines) để duy trì mạng WAN (Wide Are Network). Các đường truyền này giới hạn từ đường truyền ISDN (128 Kbps) đến đường cáp quang OC3 (optical carrier-3, 155Mbps). Mỗi mạng WAN đều có các điểm thuận lợi trên một mạng công cộng như Internet trong độ tin cậy, hiệu năng và tính an toàn, bảo mật. Nhưng để bảo trì một mạng WAN, đặc biệt khi sử dụng các đường truyền riêng, có thể trở nên quá đắt khi doanh nghiệp muốn mở rộng các chi nhánh.

Khi tính phổ biến của Internet gia tăng, các doanh nghiệp đầu tư vào nó như một phương tiện quảng bá và mở rộng các mạng mà họ sở hữu. Ban đầu, là các mạng nội bộ (Intranet) mà các site được bảo mật bằng mật khẩu được thiết kế cho việc sử dụng chỉ bởi các thành viên trong công ty.

Hình 1.1. Mô hình VPN cơ bản

![Ảnh đã được loại bỏ để giảm kích thước file]

Về căn bản, mỗi VPN (Virtual Private Network) là một mạng riêng rẽ sử dụng một mạng chung (thường là Internet) để kết nối cùng với các site (các mạng riêng lẻ) hay nhiều người sử dụng từ xa. Thay cho việc sử dụng bởi một kết nối thực, chuyên dụng như đường Leased Line, mỗi VPN sử dụng các kết nối ảo được dẫn qua đường Internet từ mạng riêng của công ty tới các site của các nhân viên từ xa.

Hình 1.2. Mô hình mạng VPN

![Ảnh đã được loại bỏ để giảm kích thước file]

Những thiết bị ở đầu mạng hỗ trợ cho mạng riêng ảo là Switch, Router và Firewall. Những thiết bị này có thể được quản trị bởi công ty hoặc các nhà cung cấp dịch vụ như ISP.

VPN được gọi là mạng ảo vì đây là một cách thiết lập một mạng riêng qua một mạng công cộng sử dụng các kết nối tạm thời. Những kết nối bảo mật được thiết lập giữa hai host , giữa host và mạng hoặc giữa hai mạng với nhau

Một VPN có thể được xây dựng bằng cách sử dụng 'Đường hầm' và 'Mã hoá'. VPN có thể xuất hiện ở bất cứ lớp nào trong mô hình OSI. VPN là sự cải tiến cơ sở hạ tầng mạng WAN mà làm thay đổi hay làm tăng thêm tính chất của các mạng cục bộ.

## I.1.2. Chức năng của VPN

VPN cung cấp ba chức năng chính:

-Sự tin cậy (Confidentiality): Người gửi có thể mã hoá các gói dữ liệu trước khi truyền chúng ngang qua mạng. Bằng cách làm như vậy, không một ai có thể

truy cập thông tin mà không được cho phép. Và nếu có lấy được thì cũng không đọc được.

- -Tính toàn vẹn dữ liệu (Data Integrity): người nhận có thể kiểm tra rằng dữ liệu đã được truyền qua mạng Internet mà không có sự thay đổi nào.
- -Xác thực nguồn gốc (Origin Authentication): Người nhận có thể xác thực nguồn gốc của gói dữ liệu, đảm bảo và công nhận nguồn thông tin.

## I.1.3. Ưu điểm

VPN có nhiều ưu điểm hơn so với các mạng Leased-Line truyền thống, bao gồm:

- -Giảm chi phí hơn so với mạng cục bộ . Tổng giá thành của việc sở hữu một mạng VPN sẽ được thu nhỏ, do chỉ phải trả ít hơn cho việc thuê băng thông đường truyền, các thiết bị mạng đường trục, và hoạt động của hệ thống. Giá thành cho việc kết nối LAN-to-LAN  giảm từ 20-30%  so với việc sử dụng đường Leased-line truyền thống. Còn đối với việc truy cập từ xa thì giảm tới từ 60-80%.
- -VPN tạo ra tính mềm dẻo cho khả năng quản lý Internet . Các VPN đã kế thừa và phát huy hơn nữa tính mềm dẻo và khả năng mở rộng kiến trúc mạng hơn là các mạng WAN truyền thống. Điều này giúp các doanh nghiệp có thể nhanh chóng và hiệu quả kinh tế cho việc mở rộng hay huỷ bỏ kết nối của các trụ sở ở xa, các người sử dụng di động…, và mở rộng các đối tác kinh doanh khi có nhu cầu.
- -VPN làm đơn giản hoá cho việc quản lý các công việc so với việc sở hữu và vận hành một mạng cục bộ . Các doanh nghiệp có thể cho phép sử dụng một vài hay tất cả các dịch vụ của mạng WAN, giúp các doanh nghiệp có thể tập trung vào các đối tượng kinh doanh chính, thay vì quản lý một mạng WAN hay mạng quay số từ xa.
- -VPN cung cấp các kiểu mạng đường hầm và làm giảm thiểu các công việc quản lý. Một Backbone IP sẽ loại bỏ các PVC (Permanent Virtual Circuit) cố định tương ứng với các giao thức kết nối như là Frame Relay và ATM. Điều này tạo ra một kiểu mạng lưới hoàn chỉnh trong khi giảm được độ phức tạp và giá thành.

Frame

Branch

![Ảnh đã được loại bỏ để giảm kích thước file]

Relay

Office

Hình 1.3. Ưu điểm của VPN so với mạng truyền thống

Một mạng VPN có được những ưu điểm của mạng cục bộ trên cơ sở hạ tầng của mạng IP công cộng  Các ưu điểm này bao gồm tính bảo mật và sử dụng đa giao . thức.

Virtual Privale Network = Tunneling + Encryption

![Ảnh đã được loại bỏ để giảm kích thước file]

Hình 1.4. Các ưu điểm của VPN

Một mạng ảo được tạo ra nhờ các giao thức đường hầm trên một kết nối IP chuẩn. GRE (Generic Routing Protocol), L2TP (Layer 2 Tunneling Protocol) và IPSec là ba phương thức đường hầm.

Một mạng cục bộ là một mạng mà đảm bảo độ tin cậy, tính toàn vẹn và xác thực, gọi tắt là CIA. Mã hoá dữ liệu và sử dụng giao thức IPSec giúp giữ liệu có thể chung chuyển trên Website với các tính chất CIA tương tự như là một mạng cục bộ.

## I.1.4. Các yêu cầu cơ bản đối với một giải pháp VPN

Có 4 yêu cầu cần đạt được khi xây dựng mạng riêng ảo.

## - Tính tương thích (compatibility)

Mỗi công ty, mỗi doanh nghiệp đều được xây dựng các hệ thống mạng nội bộ và mạng diện rộng của mình dựa trên các thủ tục khác nhau và không tuân theo một chuẩn nhất định của nhà cung cấp dịch vụ. Rất nhiều các hệ thống mạng không sử dụng các chuẩn TCP/IP vì vậykhông thể kết nối trực tiếp với Internet. Để có thể sử dụng được IP VPN tất cả các hệ thống mạng riêng đều phải được chuyển sang một hệ thống địa chỉ theo chuẩn sử dụng trong internet cũng như bổ sung các tính năng về tạo kênh kết nối ảo, cài đặt cổng kết nối Internet có chức năng trong việc chuyển đổi các thủ tục khác nhau sang chuẩn IP. Hầu hết khách hàng được hỏi yêu cầu khi chọn một nhà cung cấp dịch vụ IP VPN phải tương thích với các thiết bị hiện có của họ.

## - Tính bảo mật (security)

Tính bảo mật cho khách hàng là một yếu tố quan trọng nhất đối với một giải pháp VPN. Người sử dụng cần được đảm bảo các dữ liệu thông qua mạng VPN đạt được mức độ an toàn giống như trong một hệ thống mạng dùng riêng do họ tự xây dựng và quản lý.

Việc cung cấp tính năng bảo đảm an toàn cần đảm bảo hai mục tiêu sau:

- -Cung cấp tính năng an toàn thích hợp bao gồm: cung cấp mật khẩu cho người sử dụng trong mạng và mã hoá dữ liệu khi truyền.
- -Đơn giản trong việc duy trì quản lý, sử dụng. Đòi hỏi thuận tiện và đơn giản cho người sử dụng cũng như nhà quản trị mạng trong việc cài đặt cũng như quản trị hệ thống.

## -Tính khả dụng (Availability):

Một giải pháp VPN cần thiết phải cung cấp được tính bảo đảm về chất lượng, hiệu suất sử dụng dịch vụ cũng như dung lượng truyền.

## - Tiêu chuẩn về chất lượng dịch vụ (QoS):

Tiêu chuẩn đánh giá của một mạng lưới có khả năng đảm bảo chất lượng dịch vụ cung cấp đầu cuối đến đầu cuối. QoS liên quan đến khả năng đảm bảo độ trễ dịch vụ trong một phạm vi nhất định hoặc liên quan đến cả hai vấn đề trên

## I.2. Đường hầm và mã hóa

Chức năng chính của VPN đó là cung cấp sự bảo mật bằng cách mã hoá qua một đường hầm.

Hình 1.5. Đường hầm VPN

![Ảnh đã được loại bỏ để giảm kích thước file]

- -Đường hầm ( Tunnel ) cung cấp các kết nối logic, điểm tới điểm qua mạng IP không hướng kết nối. Điều này giúp cho việc sử dụng các ưu điểm các tính năng bảo mật. Các giải pháp đường hầm cho VPN là sử dụng sự mã hoá để bảo vệ dữ liệu không bị xem trộm bởi bất cứ những ai không được phép và để thực hiện đóng gói đa giao thức nếu cần thiết. Mã hoá được sử dụng để tạo kết nối đường hầm để dữ liệu chỉ có thể được đọc bởi người nhận và người gửi.
- -Mã hoá (Encryption) chắc chắn rằng thông tin sẽ không bị lộ đến bất kỳ ai ngoài người nhận. Khi mà càng có nhiều thông tin lưu thông trên mạng thì sự cần thiết đối với việc mã hoá thông tin càng trở nên quan trọng. Mã hoá sẽ biến đổi nội dung thông tin thành trong một văn bản mật mã. Chức năng giải mã chỉ có thể được sử dụng bởi người nhận.

## II. CÁC KIỂU VPN

VPNs nhằm hướng vào 3 yêu cầu cơ bản sau đây :

- -Có thể truy cập bất cứ lúc nào bằng điều khiển từ xa (Remote), bằng điện thoại di động, và việc liên lạc giữa các nhân viên của một cơ quan, doanh nghiệp tới các tài nguyên qua đường mạng Internet.
- -Kết nối thông tin liên lạc giữa các văn phòng chi nhánh hoặc từ các văn phòng ở xa nhau.

- -Ðược điều khiển truy nhập tài nguyên mạng khi cần thiết của khách hàng, nhà cung cấp và những đối tượng quan trọng của cơ quan, doanh nghiệp nhằm hợp tác kinh doanh.

Dựa trên những nhu cầu cơ bản trên, ngày nay VPNs đã phát triển và phân chia ra thành 3 phân loại chính sau :

- -Remote Access VPNs.
- -Intranet VPNs.
- -Extranet VPNs.

## II.1. Các VPN truy cập (Remote Access VPNs)

Giống như gợi ý của tên gọi, Remote Access VPNs cho phép truy cập bất cứ lúc nào bằng Remote, Mobile, và các thiết bị truyền thông của nhân viên các chi nhánh kết nối đến tài nguyên mạng của cơ quan, doanh nghiệp đó. Ðặc biệt là những người dùng thường xuyên di chuyển hoặc các chi nhánh văn phòng nhỏ mà không có kết nối thường xuyên đến mạng Intranet.

Các truy cập VPN thường yêu cầu một vài kiểu phần mềm Client chạy trên máy tính của người sử dụng. Kiểu VPN này thường được gọi là VPN truy cập từ xa.

Hình 1.6. Mô hình mạng VPN truy cập

![Ảnh đã được loại bỏ để giảm kích thước file]

- - Một số thành phần chính:

Remote Access Server (RAS): được đặt tại trung tâm có nhiệm vụ xác nhận và chứng nhận các yêu cầu gửi tới.

Quay số kết nối đến trung tâm, điều này sẽ làm giảm chi phí cho một số yêu cầu ở khá xa so với trung tâm.

Hỗ trợ cho những người có nhiệm vụ cấu hình, bảo trì và quản lý RAS và hỗ trợ truy cập từ xa bởi người dùng.

Bằng việc triển khai Remote Access VPNs, những người dùng từ xa hoặc các chi nhánh văn phòng chỉ cần cài đặt một kết nối cục bộ đến nhà cung cấp dịch vụ ISP hoặc ISP's POP và kết nối đến tài nguyên thông qua Internet.

Hình 1.7. Cài đặt Remote Access

![Ảnh đã được loại bỏ để giảm kích thước file]

## VPN - Thuận lợi chính của Remote Access VPNs:

- + Sự cần thiết của RAS và việc kết hợp với modem được loại trừ.
- + Sự cần thiết hỗ trợ cho người dung cá nhân được loại trừ bởi vì kết nối từ xa đã được tạo điều kiện thuận lợi bởi ISP.
- + Việc quay số từ những khoảng cách xa được loại trừ, thay vào đó là những kết nối với khoảng cách xa sẽ được thay thế bởi các kết nối cục bộ.
- + Giảm giá thành chi phí cho các kết nối với khoảng cách xa.

- + Do đây là một kết nối mang tính cục bộ, vì vậy tốc độ nối kết sẽ cao hơn so với kết nối trực tiếp đến những khoảng cách xa.
- +VPNs cung cấp khả năng truy cập đến trung tâm tốt hơn bởi vì nó hỗ trợ dịch vụ truy cập ở mức độ tối thiểu nhất cho dù có nhiều các kết nối đồng thời đến mạng.
- - Ngoài những thuận lợi trên, VPNs cũng tồn tại một số bất lợi khác như:
- + Remote Access VPNs cũng không bảo đảm được hầu hết chất lượng phục vụ.
- + Khả năng mất dữ liệu rất cao, ngoài ra là các phân đoạn của gói dữ liệu có thể đi ra ngoài và bị thất thoát.
- + Do độ phức tạp của thuật toán mã hoá, Protocol Overhead tăng đáng kể, điều này gây khó khăn cho quá trình xác nhận. Thêm vào đó, việc nén dữ liệu IP và PPP-based diễn ra vô cùng chậm chạp và tồi tệ.
- + Do phải truyền dữ liệu thông qua Internet, nên khi trao đổi các dữ liệu lớn như các gói dữ liệu truyền thông, phim ảnh, âm thanh sẽ rất chậm.

## II.2. Các VPN nội bộ (Intranet VPNs):

Intranet VPNs được sử dụng để kết nối đến các chi nhánh văn phòng của tổ chức đến Corporate Intranet (Backbone Router) sử dụng Campus Router. Theo mô hình này sẽ rất tốn chi phí do phải sử dụng hai router để thiết lập được mạng, thêm vào đó, việc triển khai, bảo trì và quản lý mạng Intranet Backbone sẽ rất tốn kém còn tùy thuộc vào lượng lưu thông trên mạng đi trên nó và phạm vi địa lý của toàn bộ mạng Intranet.

Ðể giải quyết vấn đề trên, sự tốn kém của WAN backbone được thay thế bởi các kết nối Internet với chi phí thấp, điều này có thể giảm một lượng chi phí đáng kể của việc triển khai mạng Intranet.

Intranet VPNs là một VPN nội bộ đươc sử dụng để bảo mật các kết nối giữa các địa điểm khác nhau của một công ty. Điều này cho phép tất cả các địa điểm có thể truy cập các nguồn dữ liệu được phép trong toàn bộ mạng của công ty. Các VPN nội bộ liên kết trụ sở chính, các văn phòng, và các văn phòng chi nhánh trên một cơ

sở hạ tầng chung sử dụng các kết nối mà luôn luôn được mã hoá. Kiểu VPN này thường được cấu hình như là một VPN Site-to-Site.

Hình 1.8. Mô hình mạng VPN nội bộ

![Ảnh đã được loại bỏ để giảm kích thước file]

- - Những thuận lợi chính của Intranet setup dựa trên VPN:
- + Hiệu quả chi phí hơn do giảm số lượng Router được sử dụng theo mô hình WAN backbone
- + Giảm thiểu đáng kể số lượng hỗ trợ yêu cầu người dùng cá nhân qua toàn cầu, các trạm ở một số Remote Site khác nhau.
- + Bởi vì Internet hoạt động như một kết nối trung gian, nó dễ dàng cung cấp những kết nối mới ngang hàng.
- + Kết nối nhanh hơn và tốt hơn do về bản chất kết nối đến nhà cung cấp dịch vụ, loại bỏ vấn đề về khoảng cách xa và thêm nữa giúp tổ chức giảm thiểu chi phí cho việc thực hiện Intranet.

## - Những bất lợi:

- + Bởi vì dữ liệu vẫn còn Tunnel trong suốt quá trình chia sẽ trên mạng công cộng Internet và những nguy cơ tấn công, như tấn công bằng từ chối dịch vụ (DOS Denial Of Service), vẫn còn là một mối đe doạ an toàn thông tin.
- + Khả năng mất dữ liệu trong lúc di chuyển thông tin cũng vẫn rất cao.

- + Trong một số trường hợp, nhất là khi dữ liệu là loại High-end, như các tập tin Mulltimedia, việc trao đổi dữ liệu sẽ rất chậm chạp do được truyền thông qua Internet.
- + Do là kết nối dựa trên Internet, nên tính hiệu quả không liên tục, thường xuyên, và QoS cũng không được đảm bảo.

## II.3. Các VPN mở rộng (Extranet VPNs):

Không giống như Intranet và Remote Access-based, Extranet không hoàn toàn cách li từ bên ngoài, Extranet cho phép truy cập những tài nguyên mạng cần thiết của các đối tác kinh doanh, chẳng hạn như khách hàng, nhà cung cấp, đối tác những người giữ vai trò quan trọng trong tổ chức.

Mạng Extranet rất tốn kém do có nhiều đoạn mạng riêng biệt trên Intranet kết hợp lại với nhau để tạo ra một Extranet. Ðiều này làm cho khó triển khai và quản lý do có nhiều mạng, đồng thời cũng khó khăn cho cá nhân làm công việc bảo trì và quản trị. Thêm nữa là mạng Extranet sẽ khó mở rộng do điều này sẽ làm rối toàn bộ mạng Intranet và có thể ảnh hưởng đến các kết nối bên ngoài mạng. Sẽ có những vấn đề bạn gặp phải bất thình lình khi kết nối một Intranet vào một mạng Extranet. Triển khai và thiết kế một mạng Extranet có thể là một điều khó khăn của các nhà thiết kế và quản trị mạng.

Hình 1.9. Thiết lập Extranet truyền thống

![Ảnh đã được loại bỏ để giảm kích thước file]

Các VPN mở rộng cung cấp một đường hầm bảo mật giữa các khách hàng, các nhà cung cấp, và các đối tác qua một cơ sở hạ tầng công cộng sử dụng các kết nối mà luôn luôn được bảo mật. Kiểu VPN này thường được cấu hình như là một VPN Site-to-Site. Sự khác nhau giữa một VPN nội bộ và một VPN mở rộng đó là sự truy cập mạng mà được công nhận ở một trong hai đầu cuối của VPN.

Hình 1.10. Mô hình mạng VPN mở rộng

![Ảnh đã được loại bỏ để giảm kích thước file]

- - Một số thuận lợi của Extranet:

- + Do hoạt động trên môi trường Internet, chúng ta có thể lựa chọn nhà phân phối khi lựa chọn và đưa ra phương pháp giải quyết tuỳ theo nhu cầu của tổ chức.
- + Bởi vì một phần Internet-connectivity được bảo trì bởi nhà cung cấp ISP nên cũng giảm chi phí bảo trì khi thuê nhân viên bảo trì.
- + Dễ dàng triển khai, quản lý và chỉnh sửa thông tin.
- - Một số bất lợi của Extranet:
- + Sự đe dọa về tính an toàn, như bị tấn công bằng từ chối dịch vụ vẫn còn tồn tại.
- + Tăng thêm nguy hiểm sự xâm nhập đối với tổ chức trên Extranet.
- + Vì dựa trên Internet nên khi dữ liệu là các loại High-end Data thì việc trao đổi diễn ra chậm chạp.
- + Do dựa trên Internet, QoS cũng không được bảo đảm thường xuyên.

Hình 1.11. Thiết lập Extranet VPN

![Ảnh đã được loại bỏ để giảm kích thước file]

## Chương 2

## GIAO THỨC ĐƯỜNG HẦM VPN

Giao thức đường hầm là một nền tảng trong VPN. Giao thức đường hầm đóng vai trò quan trọng trong việc thực hiện đóng gói và vận chuyển gói tin để truyền trên đường mạng công cộng. Có ba giao thức đường hầm cơ bản và được sử dụng nhiều trong thực tế và đang được sử dụng hiện nay là giao thức tầng hầm chuyển tiếp lớp 2 (L2F), giao thức đường hầm điểm tới điểm (PPTP), giao thức tầng hầm lớp 2 Layer (L2TP). Trong nghiên cứu đồ án này chúng ta sẽ đi sâu hơn và cụ thể hơn về giao thức đường hầm điểm tới điềm (PPTP). Nó liên quan đến việc thực hiện IP-VPN trên mạng công cộng.

## I. GIỚI THIỆU CÁC GIAO THỨC ĐƯỜNG HẦM

Có rất nhiều giao thức đường hầm khác nhau trong công nghệ VPN, và việc sử dụng các giao thức nào lên quan đến các phương pháp xác thực và mật mã đi kèm. Một số giao thức đường hầm phổ biến hiện nay là:

- -Giao thức tầng hầm chuyển tiếp lớp 2 (L2F).
- -Giao thức đường hầm điểm tới điểm (PPTP).
- -Giao thức tầng hầm lớp 2 (L2TP).
- -GRE
- -IPSec

Hai giao thức L2F và PPTP đều được kế thừa và phát triển dựa trên giao thức BP (Point to Point Protocol). Có thể nói PPP là một giao thức cơ bản và được sử dụng nối tiếp lớp 2, có thể sử dụng để chuyển gói tin dữ liệu qua các mạng IP và hỗ trợ đa giao thức lớp trên. Giao thức L2F được hãng Cisco nghiên cứu và phát triển độc quyền, còn PPTP được nhiều công ty cùng nhau hợp tác nghiên cứu và phát triển. Dựa vào hai giao thức trên được tổ chức kĩ thuật Internet (IETF) đã phát triển giao thức đường hầm L2TP. Và hiện nay các giao thức PPTP và L2TP được sử dụng phổ biến hơn L2F. Trong các giao thức đường hầm nói trên, giao thức IPSec là một trong nhưng giải pháp tối ưu về mặt an toàn dữ liệu của gói tin. Nó được sử

dụng các phương pháp xác thực và mật mã tương đối cao. IPSec được mang tính linh động hơn, không bị ràng buộc bởi các thuật toán xác thực hay mật mã nào cả.

## II. GIAO THỨC ĐƯỜNG HẦM ĐIỂM TỚI ĐIỂM (PPTP).

Giao thức này được nghiên cứu và phát triển bởi công ty chuyên về thiết bị công nghệ viễn thông. Trên cơ sở của giao thức này là tách các chức năng chung và riêng của việc truy nhập từ xa, dự trên cơ sở hạ tầng Internet có sẵn để tạo kết nối đường hầm giữa người dùng và mạng riêng ảo. Người dùng ở xa có thể dùng phương pháp quay số tới các nhà cung cấp dịch vụ Internet để có thể tạo đường hầm riêng để kết nối tới truy nhập tới mạng riêng ảo của người dùng đó. Giao thức PPTP được xây dựng dựa trên nền tảng của PPP, nó có thể cung cấp khả năng truy nhập tạo đường hầm thông qua Internet đến các site đích. PPTP sử dụng giao thức đóng gói tin định tuyến chung GRE được mô tả để đóng lại và tách gói PPP. Giao thức này cho phép PPTP linh hoạt trong xử lý các giao thức khác.

## II.1. Nguyên tắc hoạt động của PPTP

BP là giao thức truy nhập vào Internet và các mạng IP phổ biến hiện nay. Nó làm việc ở lớp liên kết dữ liệu trong mô hình OSI, PPP bao gồm các phương thức đóng gói, tách gói IP, là truyền đi trên chỗ kết nối điểm tới điểm từ máy này sang máy khác.

PPTP đóng các gói tin và khung dữ liệu của giao thức PPP vào các gói tin IP để truyền qua mạng IP. PPTP dùng kết nối TCP để khởi tạo và duy trì, kết thức đường hầm và dùng một gói định tuyến chung GRE để đóng gói các khung PPP. Phần tải của khung PPP có thể được mã hoá và nén lại.

PPTP sử dụng PPP để thực hiện các chức năng thiết lập và kết thức kết nối vật lý, xác định người dùng, và tạo các gói dữ liệu PPP.

PPTP có thể tồn tại một mạng IP giữa PPTP khách và PPTP chủ của mạng. PPTP khách có thể được đấu nối trực tiếp tới máy chủ thông qua truy nhập mạng NAS để thiết lập kết nối IP. Khi kết nối được thực hiện có nghĩa là người dùng đã được xác nhận. Đó là giai đoạn tùy chọn trong PPP, tuy nhiên nó luôn luôn được

cung cấp bởi ISP. Việc xác thực trong quá trình thiết lập kết nối dựa trên PPTP sử dụng các cơ chế xác thực của kết nối PPP. Một số cơ chế xác thực được sử dụng là:

- -Giao thức xác thực mở rộng EAP.
- -Giao thức xác thực có thử thách bắt tay CHAP.
- -Giao thức xác định mật khẩu PAP.

Giao thức PAP hoạt động trên nguyên tắc mật khẩu được gửi qua kết nối dưới dạng văn bản đơn giản và không có bảo mật. CHAP là giao thức cách thức mạnh hơn, sử dụng phương pháp bắt tay ba chiều để hoạt động và chống lại các tấn công quay lại bằng cách sử dụng các giá trị bí mật duy nhất và không thể đoán và giải được. PPTP cũng được các nhà phát triển công nghệ đưa vào việc mật mã và nén phần tải tin của PPP. Để mật mã phần tải tin PPP có thể sử dụng phương thức mã hoá điểm tới điểm MPPE. MPPE chỉ cung cấp mật mã trong lúc truyền dữ liệu trên đường truyền không cung cấp mật mã tại các thiết bị đầu cuối tới đầu cuối. Nếu cần sử dụng mật mã đầu cuối đến đầu cuối thì có thể dùng giao thức IPSec để bảo mật lưu lượng IP giữa các đầu cuối sau khi đường hầm PPTP được thiết lập.

Khi PPP được thiết lập kết nối, PPTP sử dụng quy luật đóng gói của PPP để đóng gói các gói truyền trong đường hầm. Để có thể dựa trên những ưu điểm của kết nối tạo bởi PPP, PPTP định nghĩa hai loại gói là điểu khiển và dữ liệu, sau đó gán chúng vào hai kênh riêng là kênh điều khiển và kênh dữ liệu. PPTP tách các kênh điều khiển và kênh dữ liệu thành những luồng điều khiển với giao thức điều khiển truyền dữ liệu TCP và luồng dữ liệu với giao thức IP. Kết nối TCP tạo ra giữa các máy khách và máy chủ được sử dụng để truyền thông báo điều khiển.

Các gói dữ liệu là dữ liệu thông thường của người dùng. Các gói điều khiển được đưa vào theo một chu kì để lấy thông tin và trạng thái kết nối và quản lý báo hiệu giữa ứng máy khách PPTP và máy chủ PPTP. Các gói điều khiển cũng được dùng để gửi các thông tin quản lý thiết bị, thông tin cấu hình giữa hai đầu đường hầm.

Kênh điều khiển được yêu cầu cho việc thiết lập một đường hầm giữa các máy khách và máy chủ PPTP. Máy chủ PPTP là một Server có sử dụng giao thức PPTP

với một giao diện được nối với Internet và một giao diện khác nối với Intranet, còn phần mềm client có thể nằm ở máy người dùng từ xa hoặc tại các máy chủ ISP.

## II.2. Nguyên tắc kết nối điều khiển đường hầm theo giao thức PPTP

Kết nối điều khiển PPTP là kết nối giữa địa chỉ IP của máy khách PPTP và địa chỉ máy chủ. Kết nối điều khiển PPTP mang theo các gói tin điều khiển và quản lý được sử dụng để duy trì đường hầm PPTP. Các bản tin này bao gồm PPTP yêu cầu phản hồi và PPTP đáp lại phải hồi định kì để phát hiện các lỗi kết nối giữa các máy trạm và máy chủ PPTP. Các gói tin của kết nối điều khiển PPTP bao gồm tiêu đề IP, tiêu đề TCP và bản tin điều khiển PPTP và tiêu đề, phần cuối của lớp liên kết dữ liệu.

Hình 2.1. Gói dữ liệu kết nối điều khiển PPTP

![Ảnh đã được loại bỏ để giảm kích thước file]

## II.3. Nguyên lý đóng gói dữ liệu đường hầm PPTP

Đóng gói khung PPP và gói định tuyến chung GRE

Dữ liệu đường hầm PPTP được đóng gói thông qua các mức được mô tả theo mô hình.

Hình 2.2. Mô hình đóng gói dữ liệu đường hầm PPTP

![Ảnh đã được loại bỏ để giảm kích thước file]

Phần tải của khung PPP ban đầu được mã hoá và đóng gói với tiêu đề PPP để tạo ra khung PPP. Khung PPP sau đó được đóng gói với phần tiêu đề của phiên bản giao thức GRE sửa đổi.

GRE là giao thức đóng gói chung, cung cấp cơ chế đóng gói dữ liệu để định tuyến qua mạng IP. Đối với PPTP, phần tiêu đề của GRE được sửa đổi một số điểm đó là một trường xác nhận dài 32 bits được thêm vào. Một bits xác nhận được sử dụng để chỉ định sự có mặt của trường xác nhận 32 bits, trường Key được thay thế

bằng trường độ dài Payload 16 bits và trường chỉ số cuộc gọi 16 bits. Trường chỉ số cuộc gọi được thiết lập bởi máy trạm PPTP trong quá trình khởi tạo đường hầm.

## Đóng gói IP

Trong khi truyền tải phần tải PPP và các tiêu đề GRE sau đó được đóng gói với một tiêu đề IP chứa các thông tin địa chỉ nguồn và đích thích hợp cho máy trạm và máy chủ PPTP.

## Đóng gói lớp liên kết dữ liệu

Để có thể truyền qua mạng LAN hay WAN thì gói tin IP cuối cùng sẽ đựơc đóng gói với một tiêu đề và phần cuối của lớp liên kết dữ liệu ở giao diện vật lý đầu ra. Như trong mạng LAN thì nếu gói tin IP đựơc gửi qua giao diện Ethernet, nó sẽ được gói với phần tiêu đề và đuôi Ethernet. Nếu gói tin IP được gửi qua đường truyền WAN điểm tới điểm nó sẽ được đóng gói với phần tiêu đề và đuôi của giao thức PPP.

## Sơ đồ đóng gói trong giao thức PPTP

Quá trình đóng gói PPTP từ một máy trạm qua kết nối truy nhập VPN từ xa sử dụng modem được mô phỏng theo hình dưới đây.

Hình 2.3. Sơ đồ đóng gói PPTP

![Ảnh đã được loại bỏ để giảm kích thước file]

-Các gói tin IP, IPX, hoặc khung NetBEUI được đưa tới giao diện ảo đại diện cho kết nối VPN bằng các giao thức tương ứng sử dụng đặc tả giao diện thiết bị mạng NDIS.

- -NDIS đưa gói tin dữ liệu tới NDIS-WAN, nơi thực hiện việc mã hoá và nén dữ liệu, cũng như cung cấp tiêu đề PPP phần tiêu đề PPP này chỉ gồm trường mã số giao thức PPP không có trường Flags và trường chuổi kiểm tra khung (FCS). Giả định trường địa chỉ và điều khiển được thoả thuận ở giao thức điều khiển đường truyền (LCP) trong quá trình kết nối PPP.
- -NDIS-WAN gửi dữ liệu tới giao thức PPTP, nơi đóng gói khung PPP với phần tiêu đề GRE. Trong tiêu đề GRE, trường chỉ số cuộc gọi được đặt giá trị thích hợp xác định đường hầm.
- -Giao thức PPTP sau đó sẽ gửi gói tin vừa tạo ra tới TCP/IP.
- -TCP/IP đóng gói dữ liệu đường hầm PPTP với phần tiêu đề IP sau đó gửi kết quả tới giao diện đại diện cho kết nối quay số tới ISP cục bộ NDIS.
- -NDIS gửi gói tin tới NDIS-WAN, cung cấp các tiêu đề và đuôi PPP.
- -NDIS-WAN gửi khung PPP kết quả tới cổng WAN tương ứng đại diện cho phần cứng quay số.
- II.4. Nguyên tắc thực hiện gói tin dữ liệu tại đầu cuối đường hầm PPTP Khi nhận được được dữ liệu đường hầm PPTP, máy trạm và máy chủ PPTP, sẽ thực hiện các bước sau:
- -Xử lý và loại bỏ gói phần tiêu đề và đuôi của lớp liên kết dữ liệu hay gói tin.
- -Xử lý và loại bỏ tiêu đề IP.
- -Xử lý và loại bỏ tiêu đề GRE và PPP.
- -Giải mã hoặc nén phần tải tin PPP.
- -Xử lý phần tải tin để nhận hoặc chuyển tiếp.

## II.5. Triển khai VPN dựa trên PPTP

Khi triển khai VPN dự trên giao thức PPTP yêu cầu hệ thống tối thiểu phải có các thành phần thiết bị như chỉ ra ở hình trên nó bao gồm:

- -Một máy chủ truy nhập mạng dùng cho phương thức quay số truy nhập bảo mật VPN.
- -Một máy chủ PPTP.
- -Máy trạm PPTP với phần mềm client cần thiết.

Hình 2.4. Các thành phần hệ thống cung cấp VPN dựa trên

![Ảnh đã được loại bỏ để giảm kích thước file]

## PPTP II.5.1. Máy chủ PPTP

Máy chủ PPTP có hai chức năng chính, đóng vai trò là điểm kết nối của đường hầm PPTP và chuyển các gói tin đến từng đường hầm mạng LAN riêng. Máy chủ PPTP chuyển các gói tin đến máy đích bằng cách xử lý gói tin PPTP để có thể được địa chỉ mạng của máy đích. Máy chủ PPTP cũng có khả năng lọc gói, bằng cách sử dụng cơ chế lọc gói PPTP máy chủ có thể ngăn cấm, chỉ có thể cho phép truy nhập vào Internet, mạng riêng hay truy nhập cả hai.

Thiết lập máy chủ PPTP tại site mạng có thể hạn chế nếu như máy chủ PPTP nằm sau tường lửa. PPTP được thiết kế sao cho chỉ có một cổng TCP 1723 được sử dụng để chuyển dữ liệu đi. Nhược điểm của cấu hình cổng này có thể làm cho bức tường lửa dễ bị tấn công. Nếu như bức tường được cấu hình để lọc gói tin thì cần phải thiết lập nó cho phép GRE đi qua.

Một thiết bị khác được đua ra năm 1998 do hãng 3Com có chức năng tương tự như máy chủ PPTP gọi là chuyển mạch đường hầm. Mục đích của chuyển mạch đường hầm là mở rộng đường hầm từ một mạng đến một mạng khác, trải rộng đường hầm từ mạng của ISP đến mạng riêng. Chuyển mạch đường hầm có thể được sử dụng tại bức tường lửa làm tăng khả năng quản lý truy nhập từ xa vào tài nguyên của mạng nội bộ. Nó có thể kiểm tra các gói tin đến và đi, giao thức của các khung PPP hoặc tên của người dùng từ xa.

## II.5.2. Phần mềm Client PPTP

Các thiết bị của ISP đã hỗ trợ PPTP thì không cần phần cứng hay phần mềm bổ sung nào cho các máy trạm, chỉ cần một kết nối PPP chuẩn. Nếu như các thiết bị của ISP không hỗ trợ PPTP thì một phần mềm ứng dụng Client vẫn có thể tạo liên kết nối bảo mật bằng cách đầu tiên quay số kết nối tới ISP bằng PPP, sau đó quay số một lần nữa thông qua cổng PPTP ảo được thiết lập ở máy trạm.

## II.5.3. Máy chủ truy nhập mạng

Máy chủ truy nhập mạng Network Access Server (NAS) còn có tên gọi là máy chủ truy nhập từ xa hay bộ tập trung truy nhập. NAS cung cấp khả năng truy nhập đường dây dựa trên phần mềm, có khả năng tính cước và có khả năng chịu đựng lỗi tại ISP, POP. NAS của ISP được thiết kế cho phép một số lượng lớn người dùng có thể quay số truy nhập vào cùng một lúc. Nếu một ISP cung cấp dịch vụ PPTP thì cần phải cài một NAS cho phép PPTP để hỗ trợ các client chạy trên các hệ điều hành khác nhau. Trong trường hợp này máy chủ ISP đóng vai trò như một client PPTP kết nối với máy chủ PPTP tại mạng riêng và máy chủ ISP trở thành một điểm cuối của đường hầm, điểm cuối còn lại máy chủ tại đầu mạng riêng

## II.6. Một số ưu nhược điểm và khả năng ứng dụng của PPTP

Ưu điểm của PPTP là được thiết kế để hoạt động ở lớp 2 trong khi IPSec chạy ở lớp 3 của mô hình OSI. Việc hỗ trợ truyền dữ liệu ở lớp 2, PPTP có thể lan truyền trong đường hầm bằng các giao thức khác IP trong khi IPSec chỉ có thể truyền các gói tin IP trong đường hầm.

PPTP là một giải pháp tạm thời vì hầu hết các nhà cung cấp dịch vụ đều có kế hoạch thay đổi PPTP bằng L2TP khi giao thức này đã được mã hoá. PPTP thích hợp cho việc quay số truy nhập với số lượng người dùng giới hạn hơn là VPN kết nối LANLAN. Một vấn đề của PPTP là xử lý xác thực người thông qua hệ điều hành. Máy chủ PPTP cũng quá tải với một số lượng người dùng quay số truy nhập hay một lưu lượng lớn dữ liệu truyền qua, điều này là một yêu cầu của kết nối LAN-LAN. Khi sử dụng VPN dựa trên PPTP mà có hỗ trợ thiết bị ISP một số quyền quản

lý phải chia sẽ cho ISP. Tính bảo mật của PPTP không mạnh bằng IPSec. Nhưng quản lý bảo mật trong PPTP lại đơn giản hơn.

Khó khăn lớn nhất đi kèm với PPTP là cơ chế yếu kém về bảo mật do nó dùng mã hóa đồng bộ trong khóa được xuất phát từ việc nó sử dụng mã hóa đối xứng là cách tạo ra khóa từ mật khẩu của người dùng. Điều này càng nguy hiểm hơn vì mật khẩu thường gửi dưới dạng phơi bày hoàn toàn trong quá trình xác nhận. Giao thức tạo đường hầm kế tiếp (L2F) được phát triển nhằm cải thiện bảo mật với mục đích này.