{"type": "object", "properties": {"exam_type": {"type": "string", "enum": ["TOEIC"], "description": "Type of exam"}, "part_number": {"type": "integer", "enum": [5, 6, 7], "description": "TOEIC Reading part number (5, 6, or 7)"}, "estimated_score": {"type": "integer", "minimum": 5, "maximum": 495, "description": "Estimated TOEIC Reading score for this part (scale 5-495)"}, "reading_passages": {"type": "array", "items": {"type": "object", "properties": {"passage_number": {"type": ["integer", "string"], "description": "Passage sequence number (integer for single passages, string like '1a', '1b' for multiple passages)"}, "part": {"type": "integer", "enum": [6, 7], "description": "TOEIC Reading part number (Part 5 has no passages)"}, "title": {"type": "string", "description": "Title of the passage"}, "content": {"type": "string", "description": "Passage content adapted from the original article for business context"}, "word_count": {"type": "integer", "description": "Number of words in the passage"}, "document_type": {"type": "string", "description": "Type of business document (email, memo, notice, article, etc.)"}}, "required": ["passage_number", "part", "title", "content", "word_count", "document_type"]}, "description": "Reading passages for Part 6 and 7 only (empty array for Part 5)"}, "questions": {"type": "array", "items": {"type": "object", "properties": {"question_number": {"type": "integer", "description": "Question sequence number"}, "part": {"type": "integer", "enum": [5, 6, 7], "description": "TOEIC Reading part number"}, "passage_reference": {"type": ["integer", "string", "null"], "description": "Reference to passage_number (null for Part 5, required for Part 6 and 7)"}, "question_text": {"type": "string", "description": "Question text with blank (___) for Part 5 and 6, or complete question for Part 7"}, "options": {"type": "object", "properties": {"A": {"type": "string"}, "B": {"type": "string"}, "C": {"type": "string"}, "D": {"type": "string"}}, "required": ["A", "B", "C", "D"]}, "correct_answer": {"type": "string", "enum": ["A", "B", "C", "D"], "description": "Correct answer (A, B, C or D)"}, "explanation": {"type": "string", "description": "Detailed explanation of why the answer is correct"}, "grammar_point": {"type": "string", "description": "Grammar point being tested (for Part 5 and 6)"}, "question_type": {"type": "string", "description": "Type of question (main idea, detail, inference, vocabulary, etc.)"}}, "required": ["question_number", "part", "question_text", "options", "correct_answer", "explanation", "question_type"]}}, "part_analysis": {"type": "object", "description": "Analysis specific to the generated part", "properties": {"grammar_focus": {"type": "array", "items": {"type": "string"}, "description": "Key grammar points tested (for Part 5 and 6)"}, "vocabulary_level": {"type": "string", "enum": ["Basic", "Intermediate", "Advanced"], "description": "Vocabulary difficulty level"}, "estimated_correct": {"type": "integer", "description": "Estimated number of correct answers for this part"}, "challenging_areas": {"type": "array", "items": {"type": "string"}, "description": "Areas that may be challenging for test takers"}, "business_context": {"type": "array", "items": {"type": "string"}, "description": "Business contexts used in the questions"}}, "required": ["vocabulary_level", "estimated_correct", "challenging_areas"]}, "improvement_suggestions": {"type": "array", "items": {"type": "string"}, "description": "Suggestions for improving performance on this part"}}, "required": ["exam_type", "part_number", "estimated_score", "reading_passages", "questions", "part_analysis", "improvement_suggestions"]}