"use client";

import Link from "next/link";
import { useState, useEffect } from "react";

export default function Home() {
    const [isScrolled, setIsScrolled] = useState(false);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 50);
        };
        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);

    return (
        <main className="min-h-screen">
            {/* Header */}
            <header
                className={`fixed w-full z-50 transition-all duration-300 ${
                    isScrolled
                        ? "bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100"
                        : "bg-white/80 backdrop-blur-sm"
                }`}
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-4">
                        <div className="flex items-center gap-3">
                            <div className="relative">
                                <img
                                    src="/logo_sci.png"
                                    alt="SCI Logo"
                                    className="w-10 h-10 object-contain"
                                />
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full animate-pulse"></div>
                            </div>
                            <div>
                                <span className="text-2xl font-bold bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 bg-clip-text text-transparent">
                                    SciHorizone
                                </span>
                                <div className="text-xs text-gray-500 font-medium">
                                    AI Exam Generator
                                </div>
                            </div>
                        </div>

                        <nav className="hidden lg:flex items-center gap-8">
                            <a
                                href="#features"
                                className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                            >
                                Features
                            </a>
                            <a
                                href="#how-it-works"
                                className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                            >
                                How it works
                            </a>
                            <a
                                href="#testimonials"
                                className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                            >
                                Testimonials
                            </a>
                            <a
                                href="#pricing"
                                className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                            >
                                Pricing
                            </a>
                            <a
                                href="#about"
                                className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                            >
                                About
                            </a>
                        </nav>

                        <div className="flex items-center gap-4">
                            <Link
                                href="/create"
                                className="btn btn-primary btn-md shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                <svg
                                    className="w-4 h-4 mr-2"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 4v16m8-8H4"
                                    />
                                </svg>
                                Create Exam
                            </Link>

                            {/* Mobile menu button */}
                            <button
                                className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
                                onClick={() =>
                                    setMobileMenuOpen(!mobileMenuOpen)
                                }
                            >
                                <svg
                                    className="w-6 h-6"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M4 6h16M4 12h16M4 18h16"
                                    />
                                </svg>
                            </button>
                        </div>
                    </div>

                    {/* Mobile menu */}
                    {mobileMenuOpen && (
                        <div className="lg:hidden border-t border-gray-100 py-4">
                            <nav className="flex flex-col gap-4">
                                <a
                                    href="#features"
                                    className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                                >
                                    Features
                                </a>
                                <a
                                    href="#how-it-works"
                                    className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                                >
                                    How it works
                                </a>
                                <a
                                    href="#testimonials"
                                    className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                                >
                                    Testimonials
                                </a>
                                <a
                                    href="#pricing"
                                    className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                                >
                                    Pricing
                                </a>
                                <a
                                    href="#about"
                                    className="text-gray-600 hover:text-primary-600 transition-colors font-medium"
                                >
                                    About
                                </a>
                            </nav>
                        </div>
                    )}
                </div>
            </header>

            {/* Hero Section */}
            <section className="relative pt-32 pb-20 md:pt-40 md:pb-32 overflow-hidden">
                {/* Background with animated gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-blue-50">
                    <div className="absolute top-0 left-0 w-full h-full">
                        <div className="absolute top-20 left-10 w-72 h-72 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob"></div>
                        <div className="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000"></div>
                        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000"></div>
                    </div>
                </div>

                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="grid lg:grid-cols-2 gap-12 items-center">
                        {/* Left content */}
                        <div className="text-center lg:text-left">
                            <div className="inline-flex items-center px-4 py-2 bg-primary-100 rounded-full text-primary-700 text-sm font-medium mb-6">
                                <svg
                                    className="w-4 h-4 mr-2"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                >
                                    <path
                                        fillRule="evenodd"
                                        d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                                AI-Powered Exam Generation
                            </div>

                            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900 leading-tight">
                                Transform
                                <span className="bg-gradient-to-r from-primary-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
                                    {" "}
                                    Scientific Papers
                                </span>
                                <br />
                                into Professional Exams
                            </h1>

                            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                                Convert academic papers into IELTS/TOEIC reading
                                comprehension exams with advanced AI technology.
                                Perfect for educators, language trainers, and
                                test preparation professionals.
                            </p>

                            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                                <Link
                                    href="/create"
                                    className="btn btn-primary btn-lg shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
                                >
                                    <svg
                                        className="w-5 h-5 mr-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M13 10V3L4 14h7v7l9-11h-7z"
                                        />
                                    </svg>
                                    Start Creating Now
                                </Link>

                                <button className="btn btn-outline btn-lg group">
                                    <svg
                                        className="w-5 h-5 mr-2 group-hover:animate-pulse"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M7 16a3 3 0 006 0v-2"
                                        />
                                    </svg>
                                    Watch Demo
                                </button>
                            </div>

                            {/* Stats */}
                            <div className="grid grid-cols-3 gap-8 mt-12 pt-8 border-t border-gray-200">
                                <div className="text-center lg:text-left">
                                    <div className="text-2xl font-bold text-gray-900">
                                        1000+
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        Exams Generated
                                    </div>
                                </div>
                                <div className="text-center lg:text-left">
                                    <div className="text-2xl font-bold text-gray-900">
                                        50+
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        Institutions
                                    </div>
                                </div>
                                <div className="text-center lg:text-left">
                                    <div className="text-2xl font-bold text-gray-900">
                                        99%
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        Accuracy Rate
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Right content - Visual */}
                        <div className="relative">
                            <div className="relative z-10">
                                {/* Main dashboard mockup */}
                                <div className="bg-white rounded-2xl shadow-2xl p-6 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                                    <div className="flex items-center gap-2 mb-4">
                                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                    </div>
                                    <div className="space-y-3">
                                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                        <div className="h-4 bg-primary-200 rounded w-1/2"></div>
                                        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                                        <div className="grid grid-cols-2 gap-3 mt-4">
                                            <div className="h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg"></div>
                                            <div className="h-20 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg"></div>
                                        </div>
                                    </div>
                                </div>

                                {/* Floating elements */}
                                <div className="absolute -top-4 -right-4 bg-primary-500 text-white p-3 rounded-xl shadow-lg animate-bounce">
                                    <svg
                                        className="w-6 h-6"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                    </svg>
                                </div>

                                <div className="absolute -bottom-4 -left-4 bg-purple-500 text-white p-3 rounded-xl shadow-lg animate-pulse">
                                    <svg
                                        className="w-6 h-6"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M13 10V3L4 14h7v7l9-11h-7z"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section
                id="features"
                className="py-24 bg-gradient-to-b from-white to-gray-50"
            >
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="text-center mb-20">
                        <div className="inline-flex items-center px-4 py-2 bg-primary-100 rounded-full text-primary-700 text-sm font-medium mb-6">
                            <svg
                                className="w-4 h-4 mr-2"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    fillRule="evenodd"
                                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                    clipRule="evenodd"
                                />
                            </svg>
                            Powerful Features
                        </div>
                        <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                            Everything you need to create
                            <span className="bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent">
                                {" "}
                                perfect exams
                            </span>
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            Our AI-powered platform provides comprehensive tools
                            for converting scientific papers into
                            professional-grade exams
                        </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        {/* Feature 1 */}
                        <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-primary-200 transform hover:-translate-y-2">
                            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative">
                                <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg
                                        className="w-8 h-8 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                        />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-primary-600 transition-colors">
                                    Smart PDF Processing
                                </h3>
                                <p className="text-gray-600 leading-relaxed mb-4">
                                    Advanced AI extracts and analyzes content
                                    from scientific papers, maintaining context
                                    and structure for optimal exam generation.
                                </p>
                                <div className="flex items-center text-primary-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                                    Learn more
                                    <svg
                                        className="w-4 h-4 ml-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {/* Feature 2 */}
                        <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-primary-200 transform hover:-translate-y-2">
                            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative">
                                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg
                                        className="w-8 h-8 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                                        />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-purple-600 transition-colors">
                                    Multiple Exam Formats
                                </h3>
                                <p className="text-gray-600 leading-relaxed mb-4">
                                    Generate IELTS and TOEIC format exams with
                                    customizable difficulty levels, question
                                    types, and assessment criteria.
                                </p>
                                <div className="flex items-center text-purple-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                                    Explore formats
                                    <svg
                                        className="w-4 h-4 ml-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {/* Feature 3 */}
                        <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-primary-200 transform hover:-translate-y-2">
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative">
                                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg
                                        className="w-8 h-8 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                        />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-blue-600 transition-colors">
                                    Flexible Export Options
                                </h3>
                                <p className="text-gray-600 leading-relaxed mb-4">
                                    Download exams as beautifully formatted
                                    PDFs, editable Word documents, or structured
                                    JSON for integration with your systems.
                                </p>
                                <div className="flex items-center text-blue-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                                    View exports
                                    <svg
                                        className="w-4 h-4 ml-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {/* Feature 4 */}
                        <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-primary-200 transform hover:-translate-y-2">
                            <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-emerald-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative">
                                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg
                                        className="w-8 h-8 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                        />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-green-600 transition-colors">
                                    Quality Assurance
                                </h3>
                                <p className="text-gray-600 leading-relaxed mb-4">
                                    Built-in validation ensures grammatical
                                    accuracy, appropriate difficulty levels, and
                                    adherence to official exam standards.
                                </p>
                                <div className="flex items-center text-green-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                                    Quality metrics
                                    <svg
                                        className="w-4 h-4 ml-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {/* Feature 5 */}
                        <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-primary-200 transform hover:-translate-y-2">
                            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 to-red-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative">
                                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg
                                        className="w-8 h-8 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M13 10V3L4 14h7v7l9-11h-7z"
                                        />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-orange-600 transition-colors">
                                    Lightning Fast
                                </h3>
                                <p className="text-gray-600 leading-relaxed mb-4">
                                    Generate comprehensive exams in minutes, not
                                    hours. Our optimized AI processes documents
                                    efficiently without compromising quality.
                                </p>
                                <div className="flex items-center text-orange-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                                    Speed test
                                    <svg
                                        className="w-4 h-4 ml-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>

                        {/* Feature 6 */}
                        <div className="group relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-primary-200 transform hover:-translate-y-2">
                            <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                            <div className="relative">
                                <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg
                                        className="w-8 h-8 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                                        />
                                    </svg>
                                </div>
                                <h3 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-indigo-600 transition-colors">
                                    Advanced Customization
                                </h3>
                                <p className="text-gray-600 leading-relaxed mb-4">
                                    Fine-tune question types, difficulty
                                    distribution, passage length, and assessment
                                    criteria to match your specific
                                    requirements.
                                </p>
                                <div className="flex items-center text-indigo-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                                    Customize now
                                    <svg
                                        className="w-4 h-4 ml-2"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M9 5l7 7-7 7"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            {/* How It Works Section */}
            <section id="how-it-works" className="py-20 bg-secondary-50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <h2 className="text-3xl font-bold text-center mb-16 text-secondary-900">
                        How It Works
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-5 text-xl font-bold">
                                1
                            </div>
                            <h3 className="text-xl font-semibold mb-3 text-secondary-900">
                                Upload PDF
                            </h3>
                            <p className="text-secondary-600">
                                Upload your scientific paper in PDF format or
                                provide a URL.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-5 text-xl font-bold">
                                2
                            </div>
                            <h3 className="text-xl font-semibold mb-3 text-secondary-900">
                                Configure Options
                            </h3>
                            <p className="text-secondary-600">
                                Select exam type, difficulty level, and other
                                customization options.
                            </p>
                        </div>

                        <div className="text-center">
                            <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-5 text-xl font-bold">
                                3
                            </div>
                            <h3 className="text-xl font-semibold mb-3 text-secondary-900">
                                Generate & Download
                            </h3>
                            <p className="text-secondary-600">
                                Our AI creates the exam and you can download it
                                in your preferred format.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-primary-600 text-white">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl font-bold mb-6">
                        Ready to create your exam?
                    </h2>
                    <p className="text-xl text-primary-100 mb-10 max-w-2xl mx-auto">
                        Start converting your scientific papers into
                        professional exams today.
                    </p>
                    <Link
                        href="/create"
                        className="btn bg-white text-primary-600 hover:bg-primary-50 focus-visible:ring-white btn-lg"
                    >
                        Get Started Now
                    </Link>
                </div>
            </section>

            {/* Footer */}
            <footer className="bg-secondary-900 text-white py-12">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex flex-col md:flex-row justify-between">
                        <div className="mb-8 md:mb-0">
                            <div className="flex items-center gap-2 mb-4">
                                <img
                                    src="/logo_sci.png"
                                    alt="SCI Logo"
                                    className="w-8 h-8 object-contain"
                                />
                                <span className="text-xl font-bold bg-gradient-to-r from-primary-400 to-primary-700 bg-clip-text text-transparent">
                                    SciHorizone
                                </span>
                            </div>
                            <p className="text-secondary-400 max-w-md">
                                Converting scientific papers into professional
                                reading comprehension exams with AI technology.
                            </p>
                        </div>

                        <div className="grid grid-cols-2 gap-8">
                            <div>
                                <h3 className="text-lg font-semibold mb-4">
                                    Quick Links
                                </h3>
                                <ul className="space-y-2">
                                    <li>
                                        <a
                                            href="#"
                                            className="text-secondary-400 hover:text-white transition-colors"
                                        >
                                            Home
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#features"
                                            className="text-secondary-400 hover:text-white transition-colors"
                                        >
                                            Features
                                        </a>
                                    </li>
                                    <li>
                                        <a
                                            href="#how-it-works"
                                            className="text-secondary-400 hover:text-white transition-colors"
                                        >
                                            How it works
                                        </a>
                                    </li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="text-lg font-semibold mb-4">
                                    Contact
                                </h3>
                                <ul className="space-y-2">
                                    <li className="text-secondary-400">
                                        <EMAIL>
                                    </li>
                                    <li className="text-secondary-400"></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div className="border-t border-secondary-800 mt-8 pt-8 text-center text-secondary-400">
                        <p>
                            &copy; {new Date().getFullYear()} SciHorizone. All
                            rights reserved.
                        </p>
                    </div>
                </div>
            </footer>
        </main>
    );
}
