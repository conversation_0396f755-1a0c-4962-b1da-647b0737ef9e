{"type": "object", "properties": {"overall_score": {"type": "number", "description": "Predicted IELTS Reading score (scale 0-9)"}, "strengths": {"type": "array", "items": {"type": "string"}, "description": "Strengths of the essay"}, "weaknesses": {"type": "array", "items": {"type": "string"}, "description": "Weaknesses of the essay"}, "reading_passages": {"type": "array", "items": {"type": "object", "properties": {"passage_number": {"type": "integer", "description": "Passage number (1-3)"}, "title": {"type": "string", "description": "Title of the reading passage"}, "content": {"type": "string", "description": "Content of the reading passage, synthesized or extracted from the original article, selecting appropriate font and vocabulary for IELTS Reading exam. Clear paragraph division, MUST BE BETWEEN 700-1200 words. MARKDOWN FORMAT"}, "word_count": {"type": "integer", "description": "Word count in the passage"}, "passage_type": {"type": "string", "enum": ["Passage 1", "Passage 2", "Passage 3"], "description": "Passage type (1: easy, 2: medium, 3: hard)"}}, "required": ["passage_number", "title", "content", "word_count", "passage_type"]}, "description": "Reading passages, synthesized from the original article"}, "passage_analysis": {"type": "array", "items": {"type": "object", "properties": {"passage_number": {"type": "integer", "description": "Passage number (1-3)"}, "difficulty_level": {"type": "string", "enum": ["Easy", "Medium", "Hard"], "description": "Difficulty level"}, "main_topic": {"type": "string", "description": "Main topic of the passage, not related to other passages"}, "question_types": {"type": "array", "items": {"type": "string"}, "description": "Exactly 3 question types for each passage, type 1 has 5 questions, type 2 has 4 questions, type 3 has 5 questions", "minItems": 3, "maxItems": 3}, "vocabulary_level": {"type": "string", "enum": ["Basic", "Intermediate", "Advanced"], "description": "Vocabulary level"}, "suggested_time": {"type": "integer", "description": "Suggested time to complete (minutes)"}, "target_word_count": {"type": "object", "properties": {"min": {"type": "integer", "description": "Minimum word count for this passage"}, "max": {"type": "integer", "description": "Maximum word count for this passage"}}, "description": "Target word count for this passage"}}}}, "questions": {"type": "array", "items": {"type": "object", "properties": {"question_number": {"type": "integer", "description": "Question number"}, "passage_reference": {"type": "integer", "description": "Reference to passage_number"}, "question_type": {"type": "string", "description": "Question type"}, "question_category": {"type": "integer", "enum": [1, 2, 3], "description": "Question category (1, 2, or 3) - each passage has 3 categories, category 1 has 5 questions, category 2 has 4 questions, category 3 has 5 questions"}, "question_text": {"type": "string", "description": "Question text"}, "options": {"type": "array", "items": {"type": "string"}, "description": "Options (if any)"}, "correct_answer": {"type": "string", "description": "Correct answer"}, "explanation": {"type": "string", "description": "Answer explanation (if any)"}}, "required": ["question_number", "passage_reference", "question_type", "question_category", "question_text", "correct_answer", "explanation"]}}, "improvement_suggestions": {"type": "array", "items": {"type": "string"}, "description": "Improvement suggestions"}}, "required": ["overall_score", "strengths", "weaknesses", "reading_passages", "passage_analysis", "questions", "improvement_suggestions"]}