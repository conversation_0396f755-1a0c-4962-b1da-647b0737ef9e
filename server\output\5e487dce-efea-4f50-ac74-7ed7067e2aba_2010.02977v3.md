## VoiceGrad: Non-Parallel Any-to-Many Voice Conversion with <PERSON><PERSON> Langevin Dynamics

<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> -In this paper, we propose a non-parallel any-tomany voice conversion (VC) method termed VoiceGrad . Inspired by Wave<PERSON>rad, a recently introduced novel waveform generation method, VoiceGrad is based upon the concepts of score matching, Langevin dynamics, and diffusion models. The idea involves training a score approximator, a fully convolutional network with a U-Net structure, to predict the gradient of the log density of the speech feature sequences of multiple speakers. The trained score approximator can be used to perform VC by using annealed Langevin dynamics or reverse diffusion process to iteratively update an input feature sequence towards the nearest stationary point of the target distribution. Thanks to the nature of this concept, VoiceGrad enables any-to-many VC, a VC scenario in which the speaker of input speech can be arbitrary, and allows for non-parallel training, which requires no parallel utterances.

Index Terms -Voice conversion (VC), non-parallel VC, any-tomany VC, score matching, Langevin dynamics, diffusion models.

## I. INTRODUCTION

Voice conversion (VC) is a technique to convert the voice of a source speaker to another voice without changing the uttered sentence. Its applications range from speaker-identity modification [1] to speaking assistance [2], [3], speech enhancement [4]-[6], bandwidth extension [7], and accent conversion [8].

While many conventional VC methods require parallel utterances to train acoustic models for feature mapping, nonparallel VC methods are ones that can work without parallel utterances or transcriptions for model training. These methods can be useful in many cases since constructing a parallel corpus is often very costly and non-scalable. Another potentially important requirement for VC methods is the ability to achieve any-to-many conversion, namely to convert speech of an arbitrary speaker to the voices of multiple speakers. Such methods are also attractive in that they can work for input speech of unknown speakers without model retraining or adaptation.

parameters of parametric distributions, such as Gaussians. The decoder represents the conditional distribution of a given set of data conditioned on a latent variable, whereas the encoder represents the posterior distribution of the latent variable. In VAEs, the encoder and decoder networks are trained to maximize the variational lower bound of the marginal loglikelihood described by these distributions. In VC methods based on VAEs [15]-[20], the encoder is responsible for converting the acoustic features of input speech into latent variables, while the decoder is responsible for doing the opposite. The basic idea is to condition the decoder on a target speaker code along with the latent variables so that the decoder can learn to generate acoustic features that are likely to be produced by the corresponding speaker and be linguistically consistent with the input speech. Hence, these methods are capable of simultaneously learning mappings to multiple speakers' voices by using a single pair of encoder and decoder networks. By intentionally not conditioning the encoder on a source speaker code, the encoder can be trained to work in a speaker-independent manner. Under this setting, these methods allow for any-to-many conversions. Subsequent to these methods, a regular (non-variational) AE-based method called AutoVC was proposed [21] and proved to be capable of handling any-to-any conversions by having the decoder take as input the speaker embeddings obtained with a speaker encoder pretrained using the generalized end-to-end loss [22].

A number of non-parallel methods have already been proposed, among which those that have attracted particular attention in recent years are based on deep generative models, such as variational autoencoders (VAEs) [9], [10], generative adversarial networks (GANs) [11], and flow-based models [12]-[14].

VAEs are a stochastic version of autoencoders (AEs), consisting of an encoder and decoder. The encoder and decoder are modeled as different neural networks that produce a set of

H. Kameoka, T. Kaneko, K. Tanaka, N. Hojo, and S. Seki are with NTT Communication Science Laboratories, Nippon Telegraph and Telephone Corporation, Atsugi, Kanagawa, 243-0198 Japan (e-mail: <EMAIL>).

GANs provide a general framework for training a generator network without an explicit definition of the generator distribution. The goal is to train a generator network so as to deceive a discriminator network, which learns to distinguish fake data generated by the generator from real data. The training process in GANs is formulated as a minimax game using an adversarial loss, in such a way that the generator progressively gets better at generating data that appear to be real, while the discriminator gets better at distinguishing them as fake data. The minimax game using the adversarial loss is shown to be equivalent to a process of fitting the implicitly defined generator distribution to the data distribution. We previously reported a non-parallel VC method [23], [24] using a GAN variant called cycle-consistent GAN (CycleGAN) [25]-[27], which was originally proposed as a method for translating images with unpaired training examples. The idea is to train a pair of mappings between one speaker's voice and another speaker's voice using a cycle-consistency loss along with the adversarial loss. While the adversarial loss is used to ensure that the output of each mapping will follow the corresponding target distribution, the cycle-consistency loss is introduced to ensure that converting input speech into another speaker's

voice and converting it back to the original speaker's voice will result in the original input speech. This encourages each mapping to make only a minimal change from the input so as not to destroy the linguistic content of the input. The cycleconsistency loss has recently proved effective also in VAEbased methods [28]. Although the CycleGAN-based method was found to work reasonably well, one limitation is that it can only handle one-to-one conversions. To overcome this limitation, we further proposed an improved version [29][31] based on another GAN variant called StarGAN [32], which offers the advantages of VAE-based and CycleGANbased methods concurrently. As with VAE-based methods, this method is capable of simultaneously learning mappings to multiple speakers' voices using a single network and thus can fully use available training data collected from multiple speakers. In addition, it works without source speaker information, and can thus handle any-to-many conversions.

Flow-based models are a class of generative models consisting of multiple invertible nonlinear layers called flows. Flows can be seen as a series of changes of variables, which gradually transform each real data sample into a random noise sample following some prespecified distribution. The basic idea is to enable the direct evaluation and optimization of a log-likelihood function, which is usually hard to compute, by using a special network architecture consisting of flows whose Jacobians and inverse functions are easy to compute. Recently, a non-parallel VC method using flow-based models has been proposed [33]. The principle is conceptually similar to VAEbased methods in the sense that the forward and inverse flows play similar roles as the encoder and decoder in a VAE.

Several VC methods based on sequence-to-sequence (S2S) models have also been proposed, including the ones we proposed previously [34]-[37]. While this approach usually requires parallel corpora for training, the recognition-synthesis approach [38]-[45], in which an automatic speech recognition (ASR) model and a decoder are cascaded to perform VC, allows for nonparallel training by separately training the ASR model and decoder using text or phoneme transcripts. In this approach, the ASR model is trained to extract a sequence of linguistic-related features, e.g., phonetic posteriorgram (PPG) or a sequence of bottleneck features (BNFs) from source speech, whereas the decoder is trained to generate speech of a target speaker from that sequence. It should be noted that the top-performing systems in Voice Conversion Challenge (VCC) 2020 [46] adopted the PPG-based recognition-synthesis approach.

Score-based generative models [47], [48] or diffusion probabilistic models [49], [50] are another notable class of generative models, different from the above, that have recently been proven to be very effective in generating images and speech waveforms. Inspired by the success of these models, in this paper we propose yet another method for non-parallel any-tomany VC based on the concepts of Langevin dynamics and reverse diffusion, and compare it objectively and subjectively with the conventional methods. The proposed model uses a neural network in a way that the behavior depends less on the distribution of inputs, which we expect to be advantageous in any-to-one or any-to-many VC tasks, especially under low- resource conditions. Another motivation for adopting a scorebased generative model or DPM for VC is the flexibility to customize the conversion process to meet various user requirements. We anticipate achieving this by combining independently pretrained classifiers or other types of score-based models to adjust the update direction at each time step of the Langevin dynamics or reverse diffusion process. This aspect is particularly appealing because it allows for customization without requiring retraining.

## II. SCORE MATCHING AND LENGEVIN DYNAMICS

We start by briefly reviewing the fundamentals of the score-based generative models, i.e., the concepts of Langevin dynamics and score matching.

For any continuously differentiable probability density p ( x ) , we call ∇ x log p ( x ) = ∂ log p ( x ) ∂ x its score function [51]. If we are given the score function of the data of interest, we can use Langevin dynamics to draw samples from the corresponding distribution: Starting from an initial point x , we can iteratively refine it in a noisy gradient ascent fashion so that the logdensity log p ( x ) will be increased

<!-- formula-not-decoded -->

Score matching [51] is a method to estimate the score function of the true distribution by optimizing a score approximator s θ ( x ) parameterized by θ . We can use the expected squared error between s θ ( x ) and ∇ x log p ( x )

where γ &gt; 0 is a step size and z is a zero-mean Gaussian white noise with variance 1 that is drawn independently at each iteration. It can be shown that when γ is sufficiently small and the number of iterations is sufficiently large, x will be an exact sample from p ( x ) under some regularity conditions. This idea is particularly attractive in that we only need to access ∇ x log p ( x ) instead of p ( x ) , which is usually very hard to estimate. Hence, given a set of training examples X = { x n } 1 ≤ ≤ n N , the focus is on how to estimate the score function from X at hand.

<!-- formula-not-decoded -->

as the objective function to be minimized with respect to θ . Here, E x ∼ p ( x ) [ · ] can be approximated as the sample mean over X . Even when the regression target ∇ x log p ( x ) is not directly accessible, there are several ways to make this problem tractable without requiring an explicit expression of p ( x ) . One is implicit score matching [51], which uses the fact that (2) is equivalent up to a constant to

<!-- formula-not-decoded -->

where ∇ x s θ ( x ) denotes the Jacobian of s θ ( x ) , and tr( ) · denotes the trace of a matrix. However, unfortunately, computing tr( ∇ x s θ ( x )) can be extremely expensive when s θ ( x ) is expressed as a deep neural network and x is high dimensional. Another technique involves denoising score matching (DSM) [52], which is noteworthy in that it can completely circumvent tr( ∇ x s θ ( x )) . The idea is to first perturb x in accordance with a pre-specified noise distribution q σ (˜ x x | ) parameterized by σ and then estimate the score of the distribution q σ (˜) x =

∫ q σ (˜ x x | ) p ( x ) d x of the perturbed version. It should be noted that q σ ( x ) can be seen as a Parzen window density estimator for p ( x ) . If we assume the noise distribution to be Gaussian q σ (˜ x x | ) = N (˜; x x , σ 2 I ) , the loss function to be minimized becomes

<!-- formula-not-decoded -->

As shown in reference [52], the optimal s θ ( x ) that minimizes (4) almost surely equals ∇ x log q σ ( x ) , and it matches ∇ x log p ( x ) when the noise variance σ 2 is small enough such that q σ ( x ) ≈ p ( x ) . The underlying intuition is that the gradient of the log density at some perturbed point ˜ x should be directed towards the original clean sample x .

Recently, attempts have been made to apply the DSM principle to image generation [47], [48] by using a neural network to describe the score approximator s θ ( x ) . A major challenge to overcome in applying DSM to image generation tasks is that real-world data including images tend to reside on a low dimensional manifold embedded in a high-dimensional space. This can be problematic in naive applications of DSM since the idea of DSM is valid only when the support of the data distribution is the whole space. In practice, the scarcity of data in low-density regions can cause difficulties in both score matching-based training and Langevin dynamics-based test sampling. To overcome this obstacle, the authors of [47] proposed a DSM variant called weighted DSM . The idea is to use multiple noise levels { σ l } L l =1 in both training and test sampling. During test sampling, the noise level is gradually decreased so that q σ l ( x ) can initially fill the whole space and eventually converge to the true distribution p ( x ) . To let the score approximator learn to behave differently in accordance with the different noise levels, they proposed using a noise conditional network to describe s θ ( x , l ) , which takes the noise level index l as an additional input. For the training objective, they proposed using a weighted sum of D σ 1 ( θ , . . . , ) D σ L ( θ )

<!-- formula-not-decoded -->

where λ l &gt; 0 is a positive constant that can be chosen arbitrarily. Based on their observation that when s θ ( x , l ) is trained to optimality, ‖ s θ ( x , l ) ‖ 2 tends to be proportional to 1 /σ l , they recommended setting λ l at σ 2 l , which results in

<!-- formula-not-decoded -->

where the expectation is taken over the training examples of x and the random samples of ˜ x ∼ N (˜; x x , σ 2 l I ) . Note that they also recommended setting { σ l } 1 ≤ ≤ l L to a positive geometric sequence such that σ 2 σ 1 = · · · = σ L σ L -1 ∈ [0 , 1] . Once s θ ( x , l ) is trained under these settings, one can produce a sample from q σ L ( x ) via an annealed version of Langevin dynamics with a special step size schedule such that γ l = ε · σ /σ 2 l 2 L for the l th noise level, where ε is a scaling factor (Algorithm 1).

Algorithm 1 Annealed Langevin dynamics [47]

<!-- formula-not-decoded -->

## III. FORMULATION AS DIFFUSION MODELS

As revealed by Ho et al. [49], DSM is closely related to diffusion probabilistic models (DPMs), or diffusion models for short. Here, we review the principle of DPMs and show that their training objective and sample generation process have similarities to those of DSM described above.

Given a data sample x 0 , normalized to have mean zero and unit variance, the diffusion process of DPM is defined as a Markov chain that gradually adds Gaussian noise to x 0 . Based on this assumed process, the model, parametrized by θ , is trained to find a reverse process that gradually reconstructs x 0 from its diffused versions. Formally, DPMs are latent variable models of the form

<!-- formula-not-decoded -->

where the notation x i j : is used to denote the set { x i , . . . , x j } and x 1 , . . . , x L are latent variables corresponding to the diffused versions of x 0 , with l = 1 , . . . , L being the time step of the diffusion process. The joint distribution q ( x 1: L | x 0 ) given x 0 is called the diffusion process, which is assumed to be a Markov chain that factors as

<!-- formula-not-decoded -->

<!-- formula-not-decoded -->

where q ( x x l | l -1 ) is defined as

(9) can be viewed as a process of scaling x l -1 by √ 1 -β l and then adding Gaussian noise with variance β l . This scaling has the role of keeping the variance of x l at unity at each time step. It is important to note that q ( x x l | 0 ) at any time step l can be obtained analytically as where α l = 1 -β l and ¯ α l = ∏ l i =1 α i , from the fact that the sum of Gaussian random variables is also Gaussian. If we use /epsilon1 ∼ N ( 0 I , ) to denote a standard Gaussian random variable, (10) can be rewritten as

<!-- formula-not-decoded -->

<!-- formula-not-decoded -->

The joint distribution p θ ( x 0: L ) is called the reverse process, which is also assumed to be a Markov chain that factors as

<!-- formula-not-decoded -->

starting from x L ∼ N ( 0 I , ) , where p θ ( x l -1 | x l ) is defined as

<!-- formula-not-decoded -->

Here, ν l is an untrained time-dependent constant and µ θ ( x l , l ) is assumed to be the ouput of a deep neural network that is parameterized by θ and conditioned on l . According to reference [49], setting ν 2 l to β l has been experimentally found to work well. Now, we can use the variational bound on the negative log-likelihood E [ -log p θ ( x 0 )] as the training objective for θ to be minimized:

<!-- formula-not-decoded -->

It is important to note that since the exact bound of L DPM ( θ ) is achieved when the Kullback-Leibler (KL) divergence between p θ ( x 1: L | x 0 ) and q ( x 1: L | x 0 ) is 0, minimizing L DPM ( θ ) with respect to θ means not only fitting p θ to the data distribution, but also making p θ and q as consistent as possible. By using (11) and a reparameterization

<!-- formula-not-decoded -->

it can be shown that (14) can be rewritten as

<!-- formula-not-decoded -->

where the expectation is taken over the training examples of x 0 and the random samples of /epsilon1 ∼ N ( 0 I , ) , and c l is a constant related to α l , ¯ α l , and ν l . See reference [49] for the detailed derivation of (16). As it has been reported by Ho et al. that better model training can be achieved by setting c l to 1 [49], c l is henceforth set to 1. The reparameterization of (15) implies representing /epsilon1 θ ( x l , l ) instead of µ θ ( x l , l ) as a neural network.

Let us now compare (16) with (6). By using the symbol x 0 instead of x and using a reparameterization ˜ = x x 0 + σ l /epsilon1 , (6) can be rewritten as

<!-- formula-not-decoded -->

where the expectation is taken over the training examples of x 0 and the random samples of /epsilon1 ∼ N ( 0 I , ) . A comparison of (17) and (16) reveals that -σ l s θ ( x 0 + σ l /epsilon1 , l ) and /epsilon1 θ ( √ ¯ α l x 0 + √ 1 -¯ α l /epsilon1 , l ) are in correspondence with each other. Hence, we will henceforth refer to /epsilon1 θ as the score approximator as well. The main difference is that the input to the network is x 0 + σ l /epsilon1 in DSM, while it is √ ¯ α l x 0 + √ 1 -¯ α l /epsilon1 in DPM. This means that in DSM the variance of the network input x l is assumed to increase after the addition of Gaussian noise, whereas in DPM the variance is assumed to remain 1 owing to the scaling of x 0 .

Once θ is trained, one can produce a sample from p θ ( x ) in accordance with the Markov chain of the reverse process (Algorithm 2). A comparison of Algorithms 1 and 2 reveals the connection between the sample generation algorithms based on the Langevin dynamics and the reverse diffusion process. Notice that in Algorithm 1, the loop counter l increments from

## Algorithm 2 Reverse diffusion process

<!-- formula-not-decoded -->

1 to L in the for-loop, whereas in Algorithm 2 it decrements from L to 1 . This is just a matter of how the diffusion time step or noise level is indexed, not an essential difference. In fact, if we define l ′ = L -l + 1 as the new loop counter in Algorithm 1, we can rewrite Algorithm 1 to decrement the loop counter l ′ from L to 1 , as in Algorithm 2. Let us now focus on the update equations in Algorithms 1 and 2. Since ∇ x l log q ( x l ) = E x 0 [ ∇ x l log q ( x x l | 0 )] (see Appendix for the proof) and ∇ x l log q ( x x l | 0 ) = -/epsilon1 √ -1 ¯ α l , we have

<!-- formula-not-decoded -->

where /epsilon1 ∼ N ( 0 I , ) . Since /epsilon1 θ ( x l , l ) is trained to predict /epsilon1 in (18), if θ is successfully trained, -/epsilon1 θ ( x l ,l ) √ 1 -¯ α l should be a good approximation of ∇ x l log q ( x l ) . Therefore, the update equation in Algorithm 2 can be interpreted as moving x l in the direction of ∇ x l log q ( x l ) with step size 1 -α l , then scaling by 1 √ α l , and finally adding ν l z . Thus, this can be seen as the same process as the update equation in Algorithm 1, except for the scaling. This scaling corresponds to the inverse of the scaling for x l -1 assumed in (9). Recall that -σ l s θ ( x l , l ) in DSM corresponds to /epsilon1 θ ( x l , l ) in DPM. Since s θ ( x l , l ) is trained to approximate ∇ x l log q ( x l ) , from (18), this implies that σ l corresponds to √ 1 -¯ α l .

## IV. RELATED WORK

As the name implies, VoiceGrad is inspired by WaveGrad [53], a recently introduced novel neural waveform generation method based on the concept of DPMs. The idea is to model the process of gradually converting a Gaussian white noise signal into a speech waveform that is best associated with a conditioning mel-spectrogram as the reverse diffusion process. After training, one can generate a waveform (in a non-autoregressive manner, unlike WaveNet) given a melspectrogram via the trained reverse diffusion process, starting from a randomly drawn noise signal. One straightforward way of adapting this idea to VC tasks would be to use the model to generate the acoustic feature sequence of target speech and that of source speech as the conditioning input. This idea may work if time-aligned parallel utterances of a speaker pair are available, but here we are concerned with achieving nonparallel any-to-many VC, as described below.

Note that several DPM-based VC methods, such as DiffSVC [54] and Diff-VC [55], have been proposed after the publication of the preprint paper on this work [56]. The idea of Diff-VC is to first convert an input mel-spectrogram to an 'average voice' mel-spectrogram using an encoder trained by

phoneme supervision on speech samples from many speakers, and then to a target speaker mel-spectrogram using the reverse diffusion process of a trained DPM. As described below, our VoiceGrad differs in that it performs the Langevin dynamics or reverse diffusion process starting directly from the melspectrogram of source speech.

## V. VOICEGRAD

## A. Key Idea

We will now describe how VoiceGrad uses the concepts of DSM, Langevin dynamics, and DPMs to achieve non-parallel any-to-many VC. Given a source speech feature sequence (e.g., mel-spectrogram), our key idea is to formulate the VC problem as finding the stationary point of the log density of target speech feature sequences nearest to the source sequence. Thus, we can naturally think of employing the Langevin dynamics or reverse diffusion process starting from a certain noise level to perform VC by using the source speech feature sequence as an initial point and moving it along the gradient direction of the log density of target speech feature sequences. From the DPM perspective, this corresponds to assuming that the source speech feature sequence is a diffused version of a target speech feature sequence. Although this process does not necessarily ensure the preservation of the linguistic content in source speech, it was experimentally found to work under some settings, as detailed later. To enable a single score approximator to predict the score functions of the feature sequences of all the K target speakers included in the training set, we also condition the score approximator network on the target speaker index k ∈ { 1 , . . . , K } . Namely, VoiceGrad uses a network /epsilon1 θ ( x , l, k ) to describe the score approximator, where x denotes the input speech feature.

Owing to the idea described above, VoiceGrad does not require the training set to consist of parallel utterances, allows the speaker of input speech at test time to be arbitrary, and can convert input speech to the voices of multiple known speakers using a single trained network.

## B. Acoustic Feature and Waveform Generation

We use the 80-dimensional log mel-spectrogram extracted from input speech as the acoustic feature sequence to be converted, and choose to use HiFi-GAN [57] for waveform generation from the converted mel-spectrogram. At training time, each element x d,m of the log mel-spectrogram x of each training example is normalized to x d,m ← ( x d,m -ψ d ) /ζ d , where d denotes the mel-filterbank channel of the melspectrogram, m denotes the frame index, and ψ d and ζ d denote the mean and standard deviation of the d -th channel elements of all the training samples. At test time, the mel-spectrogram of input speech is normalized in the same way.

## C. Training and Conversion Processes

As noted above, in VoiceGrad, the generation process of a target speaker's mel-spectrogram is treated as a gradient descent search for a probabilistically likely mel-spectrogram in

## Algorithm 3 DSM-based VoiceGrad

```
Require: { σ l } L l = L ′ , ε , T , x , k for l = L ′ to L do γ l ← · ε σ /σ 2 l 2 L for t = 1 to T do Draw z ∼ N ( 0 I , ) Update x ← x -γ l σ l /epsilon1 θ ( x , l, k ) + √ 2 γ l z end for end for return x
```

## Algorithm 4 DPM-based VoiceGrad

Require: { α l } L ′ l =1 , { ¯ α l } L ′ l =1 , x , k for

l

=

L

′

to

1

do end for

return x

<!-- formula-not-decoded -->

the DSM version, whereas in the DPM version, it is regarded as the reverse diffusion process (Fig. 1).

As in the previous study [53], using the L 1 measure instead of the L 2 measure in (6) and (16) was found to be effective in terms of both training stability and audio quality at test time. Hence, the training objectives for VoiceGrad to be minimized with respect to θ under the DSM and DPM formulations become

<!-- formula-not-decoded -->

<!-- formula-not-decoded -->

respectively, where the expectations in both equations are taken over the random samples of l ∼ U (1 , . . . , L ) , k ∼ U (1 , . . . , K ) , and /epsilon1 ∼ N ( 0 I , ) , and the training examples of x 0 ∼ p ( x 0 | k ) . Here, U ( ) · is used to denote a discrete uniform distribution over the integers in its argument. Note that -σ l s θ ( x l , l, k ) is expressed here as /epsilon1 θ ( x l , l, k ) in (19) to unify the symbols for the score approximator. Given a set of training examples X = { x ( k,n ) 0 } 1 ≤ ≤ k K, 1 ≤ ≤ n N , where N is the number of training utterances of each speaker, x ( k,n ) 0 ∈ R 80 × M k,n denote the mel-spectrogram of the n th training utterance of the k th speaker, respectively, M k,n denotes the length of x ( k,n ) 0 , the expectation E k, x 0 [ · ] can be approximated as the sample mean over X , and E /epsilon1 [ ] · can be evaluated using the Monte Carlo approximation.

Once the score approximator /epsilon1 θ is trained using (19) or (20) as a criterion, we can use Algorithm 3 or Algorithm 4 to convert an input mel-spectrogram x to the voice of speaker k . For both algorithms, we found experimentally that starting the iteration from a certain point in the middle rather than from the beginning is effective in terms of the audio quality of the converted speech. Henceforth, we use L ′ to denote the starting noise level of the iteration. Fig. 2 shows an example of the actual process of converting a male speaker's mel-spectrogram to a female voice by reverse diffusion process (Algorithm 4) starting from L ′ = 11 .

Fig. 1. Mel-spectrogram generation by reverse diffusion process

![Ảnh đã được loại bỏ để giảm kích thước file]

Fig. 2. Process of converting a male speaker's mel-spectrogram to a female voice by a reverse diffusion process starting from level 11 at test time.

![Ảnh đã được loại bỏ để giảm kích thước file]

## D. BNF Conditioning

In V-A, we noted that the Langevin dynamics or reverse diffusion process, starting from the mel-spectrogram of source speech, has been experimentally found to preserve the linguistic content in the source speech to a certain extent. However, we have also found that by designing the score approximator to incorporate the BNF sequence obtained by an ASR model from source speech, the conversion process can be guided to preserve the linguistic content and improve the intelligibility of the generated speech.

To extract the BNF sequence from a mel-spectrogram, we use the bottleneck feature extractor (BNE) proposed by Liu et al. [45]. This BNE is obtained by inserting an additional bottleneck layer between the encoder and decoder in an endto-end phoneme recognizer [58], training the entire network on a large speech recognition data corpus, and dropping all the layers subsequent to the bottleneck layer from the network after training. For more details on the architecture and training scheme of the BNE, see reference [45].

The BNF sequence is expected to contain little information other than the linguistic content of the input speech. Namely, the BNF sequence should remain virtually unchanged before and after VC. Therefore, it is expected to work even if the BNF sequence of target speech is used as an additional input to the network during the score approximator training whereas the BNF sequence of the source speech is used instead at test time. By using the BNF sequence in this way, we expect that the BNF sequence will encourage the score approximator to learn to predict the target score function using the linguistic information of the input speech as a guide. With the above expectation, the BNF sequence is obtained from the melspectrogram of each training example at training time and from the mel-spectrogram of source speech at test time.

The current model setup is such that the output of the trained

BNE becomes a sequence of 144-dimensional BNF vectors of the same length as the mel-spectrogram input to the BNE.

## E. Noise Variance Scheduling

Although the noise variances, i.e., { σ l } l in the DSM formulation and { β l } l in the DPM formulation, can be set arbitrarily, it has been experimentally reported that the choice of these variances can affect sample quality in image generation applications. For the DSM formulation, we set { σ l } 1 ≤ ≤ l L at a geometric sequence, as in [47], with common ratio σ 2 σ 1 = · · · = σ L σ L -1 ≈ 0 787 . , where L = 21 , σ 1 = 1 2 . , and σ L = 0 01 . . For the DPM formulation, we use a cosine-based schedule [50] for the noise variance setting. Specifically, we construct a schedule in terms of ¯ α l (instead of β l ) as

<!-- formula-not-decoded -->

where L = 20 . From the relation between ¯ α l and β l , we get β l = 1 -¯ α l ¯ α l -1 . To prevent β l from being too close to 1, β l is further clipped to be no larger than 0.999. η is a small offset to prevent β l from being too small when close to l = 0 , which we set at η = 0 008 . in the following experiment.

## F. Network Architecture

1) U-Net-like Structure: The architecture of the score approximator is detailed in Fig. 3. As Fig. 3 shows, it is designed to have a fully convolutional structure similar to UNet [59] that takes the mel-spectrogram of input speech as an input array and outputs an equally sized array. Note that we have also tried other types of architectures, such as AElike bottleneck architectures without skip connections, but so far we have experimentally confirmed that the current U-Netlike architecture works best. Here, the input and output of each layer are vector sequences, where 'c' and 'l' denote

Fig. 3. Network architecture of the score approximator with a U-Net-like fully convolutional structure. Here, ⊕ represents array concatenation along the channel direction. See V-F3 for details on how the noise-level and speaker indices and the BNF sequence are incorporated into the network.

![Ảnh đã được loại bỏ để giảm kích thước file]

the channel number and the length of a vector sequence, respectively. 'Conv1d', 'GLU', and 'Deconv1d' denote 1D convolution, gated linear unit (GLU) [60], and 1D transposed convolution layers, respectively. See below for the details of GLUs. 'k', 'c' and 's' denote the kernel size, output channel number, and stride size of a convolution layer, respectively. All the convolution weights are initilized using the Glorot normal initializer [61] with gain 0.5 and reparameterized using weight normalization [62].

2) Gated Linear Unit: For non-linear activation functions, we use GLUs [60]. The output of a GLU is defined as GLU ( y ) = y 1 /circledot sigmoid ( y 2 ) where y is the input, y 1 and y 2 are equally sized arrays obtained by splitting y along the channel dimension, and sigmoid is a sigmoid gate function. Like long short-term memory units, GLUs provide a linear path for the gradients while retaining non-linear capabilities, thus reducing the vanishing gradient problem for deep architectures.

3) Noise-level, Speaker, and BNF Conditioning: The noiselevel and speaker indices are incorporated into each convolution layer in the score approximator by first retrieving embedding vectors from two learnable lookup tables according to the specified indices, then repeating those vectors in the time direction to the length compatible with the input of the convolution layer, and finally concatenating the two repeated vector sequences to the input along the channel direction.

In the version that incorporates a BNF sequence p into the network, p is first fed into a strided convolution layer h with 32 output channels with a stride size r , and then the output is appended along the channel direction to the input of each convolution layer with GLU. The stride size r is appropriately chosen so that the length of the output of h is compatible with the input of the convolution layer with GLU.

## VI. EXPERIMENTS

## A. Dataset

To evaluate the performance of VoiceGrad, we conducted speaker conversion experiments. For the experiments, we used the CMU ARCTIC database [63], which consists of recordings of 18 speakers each reading the same 1,132 phonetically balanced English sentences. For the training set and the test set for a closed-set scenario, we used the utterances of two female speakers, 'clb' and 'slt', and two male speakers, 'bdl' and 'rms'. Thus, K = 4 . We also used the utterances of two male speakers, 'jmk' and 'ksp', and a female speaker, 'lnh', as the test set for an open-set scenario. All the speech signals were sampled at 16 kHz.

For each speaker, we first split the 1,132 sentences into 1,000, 100, and 32 sentences and used the 32 sentences for evaluation. To simulate a non-parallel training scenario, we further divided the 1,000 sentences equally into four groups and used the first, second, third, and fourth groups for training for speakers clb, bdl, slt, and rms, respectively, so as not to use the same sentences between different speakers. The training utterances of speakers clb, bdl, slt, and rms were about 12, 11, 11, and 14 minutes long in total, respectively. For the test set, we used the test utterances of speakers clb, bdl, slt, and rms for the closed-set scenario, and those of speakers jmk, ksp and lnh for the open-set scenario.

## B. Baseline Methods

We chose AE-based zero-shot VC method [21] (AutoVC), PPG-based one-shot VC method [45] (PPG-VC), and our previously proposed StarGAN-VC [29], [31] for comparison, as these methods, in principle, are capable of addressing manyto-many scenarios but also any-to-many situations by leveraging non-parallel corpora. It should also be noted that these methods are employed in the systems submitted for VCC2020. To run these methods, we used the source codes provided by the respective authors [64]-[66]. For StarGAN-VC, we used the Wasserstein distance as the training objective [31], [67][69]. Although in the original paper [31], StarGAN-VC used the mel-cepstral coefficient (MCC) vector sequence of source speech as the feature to be converted, in this experiment it was modified to use the mel-spectrogram instead as the feature to be converted and use HiFi-GAN [57] to generate waveforms from the converted spectrograms [66], [70].

## C. Model Setup

The score approximator network was trained using the Adam optimizer [71] with random initialization, the learning rate of 0.001, and the mini-batch size of 16. The starting noise level index L ′ was set to 4 for Algorithm 3 and 11 for Algorithm 4. The noise variances { σ l } 1 ≤ ≤ l L and { β l } 1 ≤ ≤ l L were set as described in V-E. The step size parameter ε and the iteration number T in Algorithm 3 were set at 10 -5 and 32, respectively. Thus, the number of iterations required to complete the conversion was (21 -4 + 1) × 32 = 576 for Algorithm 3 and 11 for Algorithm 4. In Algorithm 4, ν l is set to √ β l , following the guidance in reference [49].

## D. Objective Evaluation Metrics

The test set for the above experiment consisted of speech samples of each speaker reading the same sentences. We

TABLE I COMPARISONS OF THE DSM AND DPM VERSIONS

| Speakers   | Speakers    | ↓ MCD [dB]                                | ↓ MCD [dB]                                | ↑ LFC                                     | ↑ LFC                                     | ↓ CER [%]            | ↓ CER [%]            | ↑ pMOS                                    | ↑ pMOS                                    |
|------------|-------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|----------------------|----------------------|-------------------------------------------|-------------------------------------------|
| s          | t           | DSM                                       | DPM                                       | DSM                                       | DPM                                       | DSM                  | DPM                  | DSM                                       | DPM                                       |
| clb        | bdl slt rms | 9 . 06 ± . 12 7 . 24 ± . 07 8 . 45 ± . 08 | 8 . 25 ± . 12 6 . 34 ± . 07 7 . 49 ± . 08 | 0 . 13 ± . 05 0 . 49 ± . 04 0 . 33 ± . 05 | 0 . 38 ± . 05 0 . 71 ± . 03 0 . 10 ± . 05 | 14 . 6 7 . 1 15 . 8  | 13 . 7 7 . 2 12 . 8  | 3 . 21 ± . 05 3 . 56 ± . 05 3 . 50 ± . 05 | 3 . 16 ± . 05 3 . 78 ± . 06 3 . 00 ± . 05 |
| bdl        | clb slt rms | 8 . 15 ± . 11 7 . 75 ± . 10 8 . 36 ± . 12 | 7 . 08 ± . 11 7 . 18 ± . 09 7 . 33 ± . 14 | 0 . 14 ± . 05 0 . 28 ± . 04 0 . 29 ± . 05 | 0 . 17 ± . 04 0 . 41 ± . 04 0 . 44 ± . 04 | 20 . 7 16 . 3 10 . 8 | 22 . 8 16 . 9 15 . 6 | 2 . 76 ± . 05 3 . 16 ± . 06 3 . 69 ± . 04 | 3 . 04 ± . 06 3 . 43 ± . 05 3 . 77 ± . 05 |
| slt        | clb bdl rms | 7 . 07 ± . 08 9 . 00 ± . 11 8 . 56 ± . 10 | 6 . 24 ± . 08 7 . 93 ± . 12 7 . 80 ± . 11 | 0 . 46 ± . 04 0 . 22 ± . 05 0 . 28 ± . 05 | 0 . 69 ± . 03 0 . 55 ± . 04 0 . 20 ± . 05 | 10 . 7 15 . 0 17 . 2 | 11 . 6 10 . 1 12 . 1 | 3 . 23 ± . 05 3 . 09 ± . 06 3 . 49 ± . 05 | 3 . 70 ± . 05 3 . 23 ± . 05 2 . 92 ± . 05 |
| rms        | clb bdl slt | 7 . 85 ± . 08 9 . 00 ± . 13 7 . 84 ± . 12 | 7 . 05 ± . 08 7 . 95 ± . 17 7 . 51 ± . 11 | 0 . 27 ± . 05 0 . 27 ± . 06 0 . 31 ± . 05 | 0 . 07 ± . 05 0 . 29 ± . 05 0 . 08 ± . 05 | 19 . 1 5 . 9 17 . 0  | 14 . 8 9 . 8 13 . 7  | 2 . 90 ± . 06 3 . 56 ± . 04 3 . 29 ± . 05 | 3 . 10 ± . 07 3 . 72 ± . 05 3 . 27 ± . 05 |
| All pairs  | All pairs   | 8 . 19 . 04                               | 7 . 35 ± . 04                             | 0 . 29 . 01                               | 0 . 34 ± . 02                             | 14 . 2               | 13 . 4               | 3 . 29 . 02                               | 3 . 34 ± . 02                             |

±

±

±

TABLE II

EFFECT OF BNF CONDITIONING IN DSM VERSION

| Speakers   | Speakers    | ↓ MCD [dB]                                | ↓ MCD [dB]                                | ↑ LFC                                     | ↑ LFC                                     | ↓ CER [%]            | ↓ CER [%]         | ↑ pMOS                                    | ↑ pMOS                                    |
|------------|-------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|----------------------|-------------------|-------------------------------------------|-------------------------------------------|
| s          | t           | DSM                                       | DSM+BNF                                   | DSM                                       | DSM+BNF                                   | DSM                  | DSM+BNF           | DSM                                       | DSM+BNF                                   |
| clb        | bdl slt rms | 9 . 06 ± . 12 7 . 24 ± . 07 8 . 45 ± . 08 | 7 . 44 ± . 09 6 . 24 ± . 06 6 . 64 ± . 07 | 0 . 13 ± . 05 0 . 49 ± . 04 0 . 33 ± . 05 | 0 . 53 ± . 03 0 . 71 ± . 02 0 . 57 ± . 03 | 14 . 6 7 . 1 15 . 8  | 2 . 5 2 . 3 2 . 7 | 3 . 21 ± . 05 3 . 56 ± . 06 3 . 50 ± . 05 | 3 . 61 ± . 05 3 . 69 ± . 05 3 . 71 ± . 05 |
| bdl        | clb slt rms | 8 . 15 ± . 11 7 . 75 ± . 10 8 . 36 ± . 12 | 6 . 72 ± . 09 6 . 88 ± . 07 7 . 33 ± . 14 | 0 . 14 ± . 05 0 . 28 ± . 04 0 . 29 ± . 05 | 0 . 44 ± . 04 0 . 61 ± . 03 0 . 43 ± . 04 | 20 . 7 16 . 3 10 . 8 | 3 . 3 3 . 1 2 . 9 | 2 . 76 ± . 05 3 . 16 ± . 06 3 . 69 ± . 04 | 3 . 31 ± . 06 3 . 62 ± . 05 3 . 81 ± . 05 |
| slt        | clb bdl rms | 7 . 07 ± . 08 9 . 00 ± . 11 8 . 56 ± . 10 | 6 . 14 ± . 07 7 . 38 ± . 08 7 . 02 ± . 11 | 0 . 46 ± . 04 0 . 22 ± . 05 0 . 28 ± . 05 | 0 . 68 ± . 03 0 . 57 ± . 03 0 . 50 ± . 04 | 10 . 7 15 . 0 17 . 2 | 1 . 8 2 . 2 1 . 8 | 3 . 23 ± . 05 3 . 09 ± . 06 3 . 49 ± . 05 | 3 . 62 ± . 05 3 . 68 ± . 05 3 . 65 ± . 04 |
| rms        | clb bdl slt | 7 . 85 ± . 08 9 . 00 ± . 13 7 . 84 ± . 12 | 6 . 45 ± . 06 7 . 89 ± . 16 6 . 74 ± . 09 | 0 . 27 ± . 05 0 . 27 ± . 06 0 . 31 ± . 05 | 0 . 54 ± . 03 0 . 42 ± . 05 0 . 55 ± . 04 | 19 . 1 5 . 9 17 . 0  | 1 . 1 1 . 2 1 . 4 | 2 . 90 ± . 06 3 . 56 ± . 04 3 . 29 ± . 05 | 3 . 62 ± . 06 3 . 80 ± . 05 3 . 79 ± . 05 |
| All pairs  | All pairs   | 8 . 19 ± . 04                             | 6 . 91 ± . 04                             | 0 . 29 ± . 01                             | 0 . 55 ± . 01                             | 14 . 2               | 2 . 2             | 3 . 29 ± . 02                             | 3 . 66 ± . 02                             |

TABLE III EFFECT OF BNF CONDITIONING IN DPM VERSION

| Speakers   | Speakers    | ↓ MCD [dB]                                | ↓ MCD [dB]                                | ↑ LFC                                     | ↑ LFC                                     | ↓ CER [%]            | ↓ CER [%]         | ↑ pMOS                                    | ↑ pMOS                                    |
|------------|-------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|----------------------|-------------------|-------------------------------------------|-------------------------------------------|
| s          | t           | DPM                                       | DPM+BNF                                   | DPM                                       | DPM+BNF                                   | DPM                  | DPM+BNF           | DPM                                       | DPM+BNF                                   |
| clb        | bdl slt rms | 8 . 25 ± . 12 6 . 34 ± . 07 7 . 49 ± . 08 | 6 . 51 ± . 09 6 . 12 ± . 06 6 . 13 ± . 06 | 0 . 38 ± . 05 0 . 71 ± . 03 0 . 10 ± . 05 | 0 . 52 ± . 03 0 . 62 ± . 03 0 . 56 ± . 02 | 13 . 7 7 . 2 12 . 8  | 2 . 4 2 . 3 3 . 2 | 3 . 16 ± . 05 3 . 78 ± . 06 3 . 00 ± . 05 | 3 . 44 ± . 05 3 . 72 ± . 05 3 . 59 ± . 05 |
| bdl        | clb slt rms | 7 . 08 ± . 11 7 . 18 ± . 09 7 . 33 ± . 14 | 5 . 89 ± . 07 6 . 20 ± . 05 6 . 46 ± . 12 | 0 . 17 ± . 04 0 . 41 ± . 04 0 . 44 ± . 04 | 0 . 57 ± . 03 0 . 63 ± . 02 0 . 48 ± . 03 | 22 . 8 16 . 9 15 . 6 | 3 . 4 3 . 0 3 . 5 | 3 . 04 ± . 06 3 . 43 ± . 05 3 . 77 ± . 05 | 3 . 63 ± . 05 3 . 64 ± . 05 3 . 55 ± . 05 |
| slt        | clb bdl rms | 6 . 24 ± . 08 7 . 93 ± . 12 7 . 80 ± . 11 | 5 . 79 ± . 05 6 . 54 ± . 08 6 . 28 ± . 09 | 0 . 69 ± . 03 0 . 55 ± . 04 0 . 20 ± . 05 | 0 . 59 ± . 03 0 . 53 ± . 03 0 . 53 ± . 03 | 11 . 6 10 . 1 12 . 1 | 1 . 5 2 . 2 2 . 0 | 3 . 70 ± . 05 3 . 23 ± . 05 2 . 92 ± . 05 | 3 . 65 ± . 05 3 . 46 ± . 06 3 . 60 ± . 05 |
| rms        | clb bdl slt | 7 . 05 ± . 08 7 . 95 ± . 17 7 . 51 ± . 11 | 6 . 02 ± . 06 6 . 91 ± . 14 6 . 44 ± . 09 | 0 . 07 ± . 05 0 . 29 ± . 05 0 . 08 ± . 05 | 0 . 52 ± . 03 0 . 44 ± . 04 0 . 55 ± . 03 | 14 . 8 9 . 8 13 . 7  | 1 . 2 1 . 1 1 . 2 | 3 . 10 ± . 07 3 . 72 ± . 05 3 . 27 ± . 05 | 3 . 69 ± . 05 3 . 51 ± . 05 3 . 64 ± . 05 |
| All pairs  | All pairs   | 7 . 35 . 04                               | 6 . 27 ± . 03                             | 0 . 34 . 02                               | 0 . 55 ± . 01                             | 13 . 4               | 2 . 2             | 3 . 34 . 02                               | 3 . 59 ± . 01                             |

±

±

±

evaluated the objective quality of the converted speech samples using mel-cepstral distortion (MCD) [dB], log F 0 correlation coefficient (LFC), and character error rate (CER) [%]. We also evaluated the audio quality of the converted speech samples with a mean opinion score (MOS) predictor. We refer to this measure as the pseudo MOS (pMOS).

The utterance-level MCD was computed by averaging the frame-level MCDs along the dynamic time warping (DTW) path aligning the MCC vector sequences of the converted and target speech. LFC was also computed based on this DTW path. CER was evaluated using the wav2vec 2.0 model [72] (the 'Large LV-60K' architecture with an extra linear module), pre-trained on 60,000 hours of unlabeled audio from LibriLight [73] dataset, and fine-tuned on 960 hours of transcribed audio from LibriSpeech dataset [74]. To obtain the pMOS of the converted speech, Saeki's system [75] submitted to the VoiceMOS challenge 2022 [76], which exhibited a strong correlation with human MOS ratings, was used as the MOS predictor. The lower the value of MCD, the closer to 1 the LFC, the closer to 0 the CER, and the closer to 5 the pMOS, the better the performance.

## E. Comparison of DSM and DPM Formulations

First, we compare the performance of the DSM and DPM versions of VoiceGrad. Table I shows the average utterancelevel MCD and LFC, CER, and pMOS with 95% confidence intervals of the converted speech obtained with the DSM and DPM versions of VoiceGrad. As the results show, the DPM version performed slightly better than the DSM version for all the metrics. Considering these results and the fact that the DPM version required only 11 iterations to perform the conversion, while the DSM version required 576 iterations, the DPM version was confirmed to be superior in our current implementation. However, given that the CER for the ground

TABLE IV MCD [DB] COMPARISONS WITH BASELINE METHODS

## (a) Closed-set scenario

±

| Speakers   | Speakers    | StarGAN-VC                                | AutoVC                                    | PPG-VC                                    | VoiceGrad                                 |
|------------|-------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|
| s          | t           |                                           |                                           |                                           |                                           |
| clb        | bdl slt rms | 8 . 10 ± . 11 6 . 68 ± . 07 7 . 55 ± . 07 | 9 . 48 ± . 09 8 . 63 ± . 09 9 . 81 ± . 08 | 7 . 86 ± . 10 7 . 82 ± . 09 7 . 75 ± . 08 | 6 . 51 ± . 09 6 . 12 ± . 06 6 . 13 ± . 06 |
| bdl        | clb slt rms | 7 . 73 ± . 11 7 . 85 ± . 09 8 . 06 ± . 13 | 9 . 15 ± . 15 8 . 67 ± . 08 8 . 64 ± . 10 | 7 . 76 ± . 11 8 . 00 ± . 11 8 . 30 ± . 09 | 5 . 89 ± . 07 6 . 20 ± . 05 6 . 46 ± . 12 |
| slt        | clb bdl rms | 6 . 52 ± . 06 8 . 17 ± . 09 8 . 14 ± . 10 | 8 . 67 ± . 07 9 . 17 ± . 10 9 . 82 ± . 08 | 7 . 47 ± . 08 7 . 90 ± . 10 8 . 19 ± . 09 | 5 . 79 ± . 05 6 . 54 ± . 08 6 . 28 ± . 09 |
| rms        | clb bdl slt | 7 . 82 ± . 08 8 . 93 ± . 14 8 . 51 ± . 11 | 9 . 05 ± . 16 9 . 59 ± . 09 8 . 94 ± . 10 | 7 . 62 ± . 08 8 . 41 ± . 16 8 . 20 ± . 13 | 6 . 02 ± . 06 6 . 91 ± . 14 6 . 44 ± . 09 |
| All pairs  | All pairs   | 7 . 84 . 04                               | 9 . 13 . 04                               | 7 . 94 . 03                               | 6 . 27 ± . 03                             |

±

±

## (b) Open-set scenario

±

| Speakers   | Speakers        | StarGAN-VC                                              | AutoVC                                                  | PPG-VC                                                  | VoiceGrad                                               |
|------------|-----------------|---------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|
| s          | t               |                                                         |                                                         |                                                         |                                                         |
| jmk        | clb bdl slt rms | 8 . 44 ± . 11 8 . 48 ± . 12 8 . 42 ± . 08 7 . 66 ± . 11 | 8 . 78 ± . 16 9 . 10 ± . 14 8 . 33 ± . 10 8 . 44 ± . 11 | 7 . 86 ± . 10 7 . 86 ± . 10 7 . 82 ± . 09 7 . 75 ± . 08 | 6 . 30 ± . 08 6 . 76 ± . 10 6 . 40 ± . 07 6 . 57 ± . 10 |
| ksp        | clb bdl slt rms | 8 . 70 ± . 10 9 . 64 ± . 12 8 . 68 ± . 11 8 . 18 ± . 08 | 9 . 14 ± . 16 9 . 63 ± . 09 8 . 93 ± . 09 8 . 62 ± . 10 | 7 . 76 ± . 11 7 . 76 ± . 11 8 . 00 ± . 11 8 . 30 ± . 09 | 6 . 67 ± . 09 7 . 30 ± . 12 6 . 81 ± . 07 6 . 61 ± . 07 |
| lnh        | clb bdl slt rms | 7 . 18 ± . 11 8 . 16 ± . 17 7 . 40 ± . 10 8 . 08 ± . 10 | 9 . 23 ± . 18 9 . 89 ± . 11 8 . 93 ± . 12 8 . 75 ± . 13 | 7 . 47 ± . 08 7 . 90 ± . 10 7 . 90 ± . 10 8 . 19 ± . 09 | 6 . 15 ± . 09 6 . 69 ± . 13 6 . 10 ± . 05 6 . 23 ± . 07 |
| All pairs  | All pairs       | 8 . 25 . 04                                             | 8 . 98 . 04                                             | 8 . 02 . 04                                             | 6 . 55 ± . 03                                           |

±

±

truth target speech was only 1.1%, we found that both versions tended to produce speech with relatively low intelligibility in terms of CER. In the following, we show that the idea of the BNF conditioning can significantly improve the CER of the generated speech.

## F. Effect of BNF Conditioning

We evaluated the effect of the BNF conditioning described in V-D. Tables II and III display the performance of the DSM and DPM versions of VoiceGrad with and without BNF conditioning, where 'DSM+BNF' and 'DPM+BNF' refer to the DSM and DPM versions with BNF conditioning, respevtively, while 's' and 't' denote source and target, respectively. As the results show, the incorporation of the BNF sequence into the score approximator resulted in a significant performance improvement in terms of all of the metrics, especially in CER. Significant improvements were also observed in terms of LFC, MCD, and pMOS, confirming that BNF conditioning contributes not only to intelligibility but also to the intonation, speaker similarity, and audio quality of the generated speech. This suggests that linguistic-related features can facilitate the prediction of the target score function. Another finding was that while the version without BNF conditioning performed relatively less effectively in inter-gender conversions compared to intra-gender conversions, BNF conditioning improved the conversions, making them less gender-dependent.

## G. Comparison with Baseline Methods

Tables IV-VII show the MCD, LFC, CER, and pMOS results of the proposed and baseline methods under the closedset and open-set conditions. Note that the proposed method

TABLE V LFC COMPARISONS WITH BASELINE METHODS

## (a) Closed-set scenario

±

| Speakers   | Speakers    | StarGAN-VC                                | AutoVC                                    | PPG-VC                                    | VoiceGrad                                 |
|------------|-------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|
| s          | t           |                                           |                                           |                                           |                                           |
| clb        | bdl slt rms | 0 . 43 ± . 05 0 . 71 ± . 03 0 . 03 ± . 04 | 0 . 23 ± . 06 0 . 37 ± . 06 0 . 25 ± . 05 | 0 . 49 ± . 04 0 . 64 ± . 04 0 . 35 ± . 05 | 0 . 52 ± . 03 0 . 62 ± . 03 0 . 56 ± . 02 |
| bdl        | clb slt rms | 0 . 44 ± . 05 0 . 56 ± . 04 0 . 12 ± . 04 | 0 . 02 ± . 06 0 . 17 ± . 06 0 . 11 ± . 05 | 0 . 51 ± . 04 0 . 55 ± . 05 0 . 28 ± . 06 | 0 . 57 ± . 03 0 . 63 ± . 02 0 . 46 ± . 03 |
| slt        | clb bdl rms | 0 . 69 ± . 03 0 . 43 ± . 05 0 . 09 ± . 04 | 0 . 26 ± . 06 0 . 19 ± . 07 0 . 20 ± . 06 | 0 . 62 ± . 04 0 . 53 ± . 04 0 . 24 ± . 06 | 0 . 59 ± . 03 0 . 53 ± . 03 0 . 53 ± . 03 |
| rms        | clb bdl slt | 0 . 41 ± . 04 0 . 18 ± . 06 0 . 25 ± . 05 | 0 . 03 ± . 06 0 . 27 ± . 05 0 . 46 ± . 04 | 0 . 57 ± . 03 0 . 43 ± . 06 0 . 52 ± . 05 | 0 . 52 ± . 03 0 . 44 ± . 04 0 . 55 ± . 03 |
| All pairs  | All pairs   | 0 . 36 . 02                               | 0 . 21 . 02                               | 0 . 48 . 01                               | 0 . 55 ± . 01                             |

±

±

## (b) Open-set scenario

±

| Speakers   | Speakers        | StarGAN-VC                                              | AutoVC                                                    | PPG-VC                                                  | VoiceGrad                                               |
|------------|-----------------|---------------------------------------------------------|-----------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|
| s          | t               |                                                         |                                                           |                                                         |                                                         |
| jmk        | clb bdl slt rms | 0 . 25 ± . 04 0 . 50 ± . 05 0 . 33 ± . 05 0 . 39 ± . 05 | 0 . 04 ± . 06 0 . 10 ± . 06 0 . 15 ± . 05 0 . 05 ± . 05   | 0 . 56 ± . 04 0 . 57 ± . 04 0 . 62 ± . 04 0 . 35 ± . 05 | 0 . 56 ± . 03 0 . 50 ± . 03 0 . 61 ± . 03 0 . 53 ± . 03 |
| ksp        | clb bdl slt rms | 0 . 33 ± . 05 0 . 08 ± . 06 0 . 28 ± . 05 0 . 17 ± . 04 | - 0 . 01 ± . 06 0 . 13 ± . 06 0 . 28 ± . 05 0 . 17 ± . 05 | 0 . 45 ± . 05 0 . 34 ± . 06 0 . 42 ± . 05 0 . 26 ± . 05 | 0 . 51 ± . 03 0 . 42 ± . 03 0 . 53 ± . 03 0 . 49 ± . 03 |
| lnh        | clb bdl slt rms | 0 . 59 ± . 03 0 . 31 ± . 04 0 . 59 ± . 03 0 . 10 ± . 03 | - 0 . 05 ± . 05 0 . 08 ± . 06 0 . 17 ± . 05 0 . 07 ± . 05 | 0 . 54 ± . 04 0 . 39 ± . 05 0 . 57 ± . 04 0 . 33 ± . 04 | 0 . 55 ± . 03 0 . 49 ± . 03 0 . 60 ± . 03 0 . 52 ± . 03 |
| All pairs  | All pairs       | 0 . 33 . 02                                             | 0 . 10 . 02                                               | 0 . 45 . 01                                             | 0 . 53 ± . 01                                           |

±

±

TABLE VI CER [%] COMPARISONS WITH BASELINE METHODS

## (a) Closed-set scenario

| Speakers   | Speakers    | StarGAN-VC            | AutoVC                  | PPG-VC               | VoiceGrad            |
|------------|-------------|-----------------------|-------------------------|----------------------|----------------------|
| s          | t           | StarGAN-VC            | AutoVC                  | PPG-VC               | VoiceGrad            |
| clb        | bdl slt rms | 3 . 77 1 . 59 7 . 26  | 74 . 96 73 . 89 71 . 57 | 3 . 44 3 . 03 4 . 10 | 2 . 44 2 . 35 3 . 17 |
| bdl        | clb slt rms | 5 . 25 5 . 69 4 . 19  | 71 . 56 72 . 06 71 . 95 | 3 . 96 4 . 08 4 . 29 | 3 . 40 2 . 96 3 . 55 |
| slt        | clb bdl rms | 1 . 18 5 . 33 14 . 55 | 71 . 70 73 . 68 70 . 47 | 2 . 19 2 . 11 2 . 60 | 1 . 47 2 . 21 1 . 97 |
| rms        | clb bdl slt | 4 . 95 1 . 32 6 . 35  | 71 . 89 74 . 68 78 . 10 | 1 . 15 1 . 34 1 . 54 | 1 . 17 1 . 09 1 . 15 |
| All pairs  | All pairs   | 5 . 12                | 73 . 04                 | 2 . 82               | 2 . 24               |

## (b) Open-set scenario

| Speakers   | Speakers        | StarGAN-VC                      | AutoVC                          | PPG-VC                          | VoiceGrad                       |
|------------|-----------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|
| s          | t               | StarGAN-VC                      | AutoVC                          | PPG-VC                          | VoiceGrad                       |
| jmk        | clb bdl slt rms | 14 . 48 3 . 53 14 . 88 3 . 26   | 72 . 71 73 . 31 73 . 80 72 . 03 | 3 . 01 2 . 71 3 . 06 3 . 40     | 3 . 51 3 . 48 3 . 03 3 . 59     |
| ksp        | clb bdl slt rms | 25 . 18 10 . 78 26 . 79 13 . 44 | 72 . 05 75 . 28 76 . 96 71 . 35 | 10 . 64 11 . 43 12 . 67 13 . 11 | 12 . 45 13 . 36 13 . 93 12 . 01 |
| lnh        | clb bdl slt rms | 2 . 13 5 . 45 2 . 19 10 . 16    | 74 . 19 76 . 00 75 . 40 74 . 24 | 2 . 11 2 . 35 1 . 94 2 . 54     | 2 . 22 2 . 40 2 . 16 3 . 03     |
| All pairs  | All pairs       | 11 . 02                         | 73 . 94                         | 5 . 75                          | 6 . 26                          |

here refers to the DPM version with BNF conditioning. For reference, the MCD, LFC, CER, and pMOS of each source speaker's speech are shown in Table VIII.

As the results indicate, among the baseline methods, PPGVC excelled in nearly all metrics, particularly in CER and pMOS, highlighting its capability to produce highly intelligi-

TABLE VII PMOS COMPARISONS WITH BASELINE METHODS

## (a) Closed-set scenario

±

| Speakers   | Speakers    | StarGAN-VC                                | AutoVC                                    | PPG-VC                                    | VoiceGrad                                 |
|------------|-------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|-------------------------------------------|
| s          | t           |                                           |                                           |                                           |                                           |
| clb        | bdl slt rms | 2 . 44 ± . 05 3 . 67 ± . 06 2 . 52 ± . 05 | 1 . 24 ± . 00 1 . 32 ± . 01 1 . 30 ± . 01 | 3 . 58 ± . 05 3 . 70 ± . 06 3 . 25 ± . 06 | 3 . 44 ± . 05 3 . 72 ± . 06 3 . 59 ± . 05 |
| bdl        | clb slt rms | 2 . 44 ± . 06 2 . 53 ± . 06 2 . 85 ± . 06 | 1 . 26 ± . 01 1 . 34 ± . 01 1 . 26 ± . 01 | 3 . 58 ± . 05 3 . 68 ± . 06 3 . 38 ± . 07 | 3 . 63 ± . 05 3 . 64 ± . 05 3 . 55 ± . 05 |
| slt        | clb bdl rms | 3 . 75 ± . 06 2 . 31 ± . 05 2 . 44 ± . 06 | 1 . 28 ± . 01 1 . 25 ± . 01 1 . 31 ± . 01 | 3 . 54 ± . 06 3 . 52 ± . 05 3 . 02 ± . 07 | 3 . 65 ± . 05 3 . 46 ± . 06 3 . 60 ± . 05 |
| rms        | clb bdl slt | 2 . 20 ± . 06 2 . 99 ± . 04 2 . 27 ± . 06 | 1 . 25 ± . 00 1 . 23 ± . 00 1 . 30 ± . 01 | 3 . 75 ± . 05 3 . 79 ± . 06 3 . 73 ± . 06 | 3 . 69 ± . 06 3 . 51 ± . 05 3 . 64 ± . 05 |
| All pairs  | All pairs   | 2 . 70 . 03                               | 1 . 28 . 00                               | 3 . 54 . 02                               | 3 . 59 ± . 02                             |

±

±

## (b) Open-set scenario

±

| Speakers   | Speakers        | StarGAN-VC                                              | AutoVC                                                  | PPG-VC                                                  | VoiceGrad                                               |
|------------|-----------------|---------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|
| s          | t               |                                                         |                                                         |                                                         |                                                         |
| jmk        | clb bdl slt rms | 2 . 10 ± . 05 3 . 23 ± . 05 2 . 16 ± . 06 3 . 37 ± . 05 | 1 . 26 ± . 01 1 . 25 ± . 01 1 . 40 ± . 02 1 . 26 ± . 01 | 3 . 70 ± . 05 3 . 78 ± . 05 3 . 79 ± . 06 3 . 50 ± . 05 | 3 . 71 ± . 05 3 . 55 ± . 05 3 . 66 ± . 06 3 . 69 ± . 04 |
| ksp        | clb bdl slt rms | 2 . 32 ± . 06 2 . 82 ± . 06 2 . 46 ± . 05 2 . 43 ± . 05 | 1 . 26 ± . 01 1 . 24 ± . 00 1 . 37 ± . 01 1 . 26 ± . 01 | 3 . 45 ± . 05 3 . 56 ± . 05 3 . 48 ± . 06 3 . 13 ± . 06 | 3 . 59 ± . 06 3 . 36 ± . 05 3 . 50 ± . 05 3 . 55 ± . 05 |
| lnh        | clb bdl slt rms | 2 . 92 ± . 06 2 . 48 ± . 04 2 . 77 ± . 06 2 . 44 ± . 05 | 1 . 28 ± . 01 1 . 27 ± . 01 1 . 41 ± . 02 1 . 28 ± . 01 | 3 . 70 ± . 05 3 . 73 ± . 05 3 . 72 ± . 06 3 . 36 ± . 06 | 3 . 77 ± . 06 3 . 56 ± . 05 3 . 76 ± . 05 3 . 65 ± . 05 |
| All pairs  | All pairs       | 2 . 62 . 02                                             | 1 . 30 . 00                                             | 3 . 57 . 02                                             | 3 . 61 ± . 02                                           |

±

±

ble and high-quality speech. Remarkably, VoiceGrad slightly surpassed this top-performing baseline in terms of CER and pMOS and notably outperformed it in MCD and LFC. This suggests that the speech produced by VoiceGrad is as clear and natural as or even more so than that produced by PPG-VC, while having features more similar to the target speaker. This was also confirmed by our subjective evaluation test, which will be discussed in detail in the following section. StarGANVC performed one step below PPG-VC and VoiceGrad in overall performance. Nevertheless, the low CER confirms that StarGAN-VC's intelligibility in the generated speech was comparable to PPG-VC and VoiceGrad. AutoVC performed worse than the other methods, even with the authors' implementation as-is, likely due to its strong dependence on dataset domains for optimal architecture and hyperparameters. While some improvement might have been possible with specific tuning for the CMU ARCTIC dataset, surpassing PPG-VC appears challenging for AutoVC, as PPG-VC has consistently shown to outperform AutoVC in both audio quality and speaker similarity according to the results in VCC2020.

While all the tested methods are expected to handle any-tomany conversions, it is crucial to evaluate their robustness to speech input from unknown speakers. This can be confirmed in the differences in each method's performance between the closed-set and open-set conditions. Among the tested methods, AutoVC and PPG-VC exhibited little difference in performance between closed-set and open-set conditions. In contrast, StarGAN-VC and VoiceGrad showed slight performance degradation in MCD and CER in the open-set condition. This indicates the high robustness of AutoVC and PPG-VC in converting speech input from unknown speakers but suggests issues with StarGAN-VC and VoiceGrad in this regard. Nevertheless, VoiceGrad's performance was the best among the tested methods, except for CER. In terms of CER, the converted speech from the source speaker ksp consistently exhibited a relatively high error rate for all the methods. This can be attributed to the fact that speaker ksp is an Indian-accented English speaker and indicates challenges with handling accented speech in all the methods.

As a reference, we also evaluated the MCD, LFC, CER, and pMOS for the speech converted by Diff-VC [55] under the same conditions as above. The results are shown in Table IX. According to the results, while Diff-VC showed impressive performance in terms of pMOS, VoiceGrad showed superior performance in all the other metrics. Despite VoiceGrad being built on seemingly strong assumptions, as pointed out by the authors of Diff-VC, its success hinges on a crucial factor: how closely the distribution of the log mel-spectrogram difference between source and target utterances resembles a Gaussian. While its validity remains uncertain, the fact that VoiceGrad performs reasonably well may suggest that this assumption is not entirely inaccurate.

## H. Real-Time Factor for Mel-Spectrogram Conversion

The real-time factor of the computation time required for the mel-spectrogram conversion for each of the tested methods is shown in Table X. As Table X shows, the DSM version of VoiceGrad was considerably slower than the other methods, while the DPM version was nearly as fast as PPG-VC. This was due to the cosine-based noise variance scheduling, which allowed the reverse diffusion process with considerably fewer steps to produce high-quality mel-spectrograms. We also found that BNF conditioning did not have a significant impact on computation time. The fastest was StarGAN-VC, followed by AutoVC. All algorithms were implemented in PyTorch and run on a single Tesla V100 SXM2 GPU with a 32.0 GB memory and an Intel(R) Xeon(R) Gold 5218 16-core CPU @ 2.30GHz.

## I. Subjective Listening Tests

We conducted mean opinion score (MOS) tests to compare the audio quality and speaker similarity of the converted speech samples generated by the proposed and baseline methods. For the proposed method, we included samples of both the DPM version with and without BNF conditioning. For these tests, we used samples obtained under the close-set and openset conditions, as in VI-G. Sixteen listeners participated in both tests. The tests were conducted online, where each participant was asked to use a headphone in a quiet environment.

With the audio quality test, we included the real speech samples of the target speakers as reference. Each listener was asked to evaluate the naturalness of each sample on a five-point scale by selecting 5: Excellent, 4: Good, 3: Fair, 2: Poor, or 1: Bad for each utterance. The scores with 95% confidence intervals are shown in Table XI. With the speaker similarity test, each listener was given a converted speech sample and a real speech sample of the corresponding target speaker and asked to evaluate how likely they were produced by the same speaker on a four-point scale by selecting 4: Same (sure), 3:

TABLE VIII MCD [DB], LFC, CER [%], AND PMOS OF SOURCE SPEECH

| Speakers   | Speakers        | ↓ MCD [dB]                                               | LFC                                                     | CER [%]   | pMOS   |
|------------|-----------------|----------------------------------------------------------|---------------------------------------------------------|-----------|--------|
| s          | t               |                                                          | ↑                                                       | ↓         | ↑      |
| clb        | bdl slt rms     | 9 . 62 ± . 11 7 . 52 ± . 07 9 . 95 ± . 09                | 0 . 30 ± . 06 0 . 63 ± . 04 0 . 30 ± . 05               | 1 . 22    | 4 . 34 |
| bdl        | clb slt rms     | 9 . 62 ± . 11 9 . 69 ± . 10 9 . 58 . 12                  | 0 . 30 ± . 06 0 . 35 ± . 06 0 . 32 ± . 06               | 1 . 67    | 4 . 24 |
| slt        | clb bdl rms     | ± 7 . 52 ± . 07 9 . 69 ± . 10 9 . 91 ± . 08              | 0 . 63 ± . 04 0 . 35 ± . 06 0 . 30 ± . 06               | 0 . 76    | 4 . 36 |
| rms        | clb bdl slt     | 9 . 95 ± . 09 9 . 58 ± . 12 9 . 91 ± . 08                | 0 . 30 ± . 05 0 . 32 ± . 06 0 . 30 ± . 06               | 0 . 71    | 4 . 40 |
| jmk        | clb bdl slt rms | 9 . 66 ± . 09 9 . 61 ± . 10 9 . 57 ± . 08 8 . 95 ± . 10  | 0 . 11 ± . 07 0 . 46 ± . 06 0 . 25 ± . 07 0 . 37 ± . 06 | 1 . 47    | 4 . 06 |
| ksp        | clb bdl slt rms | 9 . 80 ± . 10 10 . 22 ± . 12 9 . 53 ± . 10 8 . 72 ± . 08 | 0 . 16 ± . 07 0 . 10 ± . 07 0 . 21 ± . 06 0 . 28 ± . 05 | 2 . 73    | 3 . 99 |
| lnh        | clb bdl slt rms | 8 . 68 ± . 12 8 . 50 ± . 17 8 . 87 ± . 12 9 . 13 . 09    | 0 . 56 ± . 04 0 . 33 ± . 06 0 . 47 ± . 06 0 . 30 . 05   | 1 . 28    | 4 . 24 |

±

±

TABLE IX MCD [DB], LFC, CER [%], AND PMOS OBTAINED WITH DIFF-VC [55] (a) Closed-set scenario

±

| Speakers   | Speakers    | ↓ MCD [dB]                                | ↑ LFC                                     | ↓ CER [%]            | ↑ pMOS                                    |
|------------|-------------|-------------------------------------------|-------------------------------------------|----------------------|-------------------------------------------|
| s          | t           | ↓ MCD [dB]                                | ↑ LFC                                     | ↓ CER [%]            | ↑ pMOS                                    |
| clb        | bdl slt rms | 7 . 75 ± . 10 7 . 44 ± . 08 7 . 75 ± . 10 | 0 . 26 ± . 05 0 . 30 ± . 05 0 . 21 ± . 04 | 2 . 81 2 . 90 3 . 39 | 3 . 86 ± . 05 3 . 97 ± . 05 3 . 77 ± . 05 |
| bdl        | clb slt rms | 9 . 62 ± . 11 8 . 45 ± . 16 8 . 83 ± . 15 | 0 . 30 ± . 06 0 . 22 ± . 05 0 . 15 ± . 05 | 3 . 99 3 . 61 4 . 02 | 3 . 76 ± . 06 3 . 86 ± . 06 3 . 67 ± . 05 |
| slt        | clb bdl rms | 8 . 03 ± . 12 7 . 73 ± . 10 8 . 18 ± . 13 | 0 . 25 ± . 04 0 . 28 ± . 05 0 . 19 ± . 04 | 2 . 70 2 . 30 3 . 06 | 3 . 85 ± . 06 3 . 85 ± . 05 3 . 71 ± . 04 |
| rms        | clb bdl slt | 8 . 08 ± . 10 8 . 41 ± . 15 7 . 79 ± . 13 | 0 . 26 ± . 05 0 . 20 ± . 06 0 . 27 ± . 05 | 2 . 41 2 . 24 2 . 58 | 3 . 85 ± . 05 3 . 84 ± . 05 3 . 96 ± . 05 |
| All pairs  | All pairs   | 8 . 01 . 04                               | 0 . 24 . 01                               | 3 . 00               | 3 . 83 . 02                               |

±

±

## (b) Open-set scenario

±

| Speakers   | Speakers        | ↓ MCD [dB]                                              | ↑ LFC                                                   | ↓ CER [%]                       | ↑ pMOS                                                    |
|------------|-----------------|---------------------------------------------------------|---------------------------------------------------------|---------------------------------|-----------------------------------------------------------|
| s          | t               | ↓ MCD [dB]                                              | ↑ LFC                                                   | ↓ CER [%]                       | ↑ pMOS                                                    |
| jmk        | clb bdl slt rms | 8 . 11 ± . 11 8 . 03 ± . 13 7 . 55 ± . 09 8 . 16 ± . 11 | 0 . 24 ± . 05 0 . 24 ± . 06 0 . 31 ± . 05 0 . 18 ± . 06 | 4 . 66 4 . 35 4 . 08 4 . 65     | 3 . 96 ± . 03 3 . 97 ± . 05 4 . 06 ± . 04 3 . 87 . 04     |
| ksp        | clb bdl slt rms | 8 . 51 ± . 10 8 . 89 ± . 15 8 . 26 ± . 11 8 . 16 ± . 08 | 0 . 22 ± . 05 0 . 19 ± . 06 0 . 25 ± . 05 0 . 13 ± . 05 | 32 . 70 31 . 83 33 . 19 28 . 93 | ± 3 . 75 ± . 05 3 . 73 ± . 05 3 . 97 ± . 06 3 . 60 ± . 05 |
| lnh        | clb bdl slt rms | 7 . 94 ± . 12 7 . 84 ± . 16 7 . 19 ± . 09 7 . 74 ± . 11 | 0 . 31 ± . 04 0 . 24 ± . 05 0 . 34 ± . 05 0 . 24 ± . 04 | 3 . 09 3 . 25 2 . 52 2 . 99     | 3 . 99 ± . 05 3 . 97 ± . 05 4 . 03 ± . 05 3 . 80 ± . 04   |
| All pairs  | All pairs       | 8 . 03 . 04                                             | 0 . 24 . 01                                             | 13 . 02                         | 3 . 88 . 01                                               |

±

±

TABLE X REAL-TIME FACTOR COMPARISONS

| StarGAN-VC   | AutoVC   | PPG-VC   | VoiceGrad   | VoiceGrad   | VoiceGrad   |
|--------------|----------|----------|-------------|-------------|-------------|
| StarGAN-VC   | AutoVC   | PPG-VC   | DSM         | DPM         | DPM+BNF     |
| 0.0014       | 0.0057   | 0.0245   | 1.180       | 0.0221      | 0.0235      |

Same (not sure), 2: Different (not sure), or 1: Different (sure). The scores with 95% confidence intervals are shown in Table XII.

in VoiceGrad, the former was significantly higher in both audio quality and speaker similarity, confirming the effectiveness of BNF conditioning. Upon listening to the actual speech samples, it becomes clear that there is often noticeable phoneme distortion in the speech converted without BNF conditioning. In contrast, the version with BNF conditioning demonstrates a significant improvement in intelligibility. Among the baseline methods, PPG-VC performed best in terms of audio quality but fell short of StarGAN-VC in terms of speaker similarity, and AutoVC performed worst in both tests. The

Comparing the versions with and without BNF conditioning

TABLE XI RESULTS OF THE MOS TEST

| Scenario            | Real          | StarGAN-VC                  | AutoVC                      | PPG-VC                      | VoiceGrad                   | VoiceGrad                   |
|---------------------|---------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|
| Scenario            | Real          | StarGAN-VC                  | AutoVC                      | PPG-VC                      | DPM                         | DPM+BNF                     |
| Closed-set Open-set | 4 . 82 ± . 12 | 2 . 66 ± . 09 2 . 61 ± . 08 | 1 . 19 ± . 05 1 . 23 ± . 05 | 3 . 65 ± . 11 3 . 70 ± . 10 | 3 . 22 ± . 10 3 . 13 ± . 10 | 3 . 77 ± . 11 3 . 81 ± . 10 |
|                     | 4 . 82 ± . 12 |                             |                             |                             |                             |                             |

TABLE XII

## RESULTS OF SPEAKER SIMILARITY TEST

| Scenario   | StarGAN-VC    | AutoVC        | PPG-VC        | VoiceGrad     | VoiceGrad     |
|------------|---------------|---------------|---------------|---------------|---------------|
|            |               |               |               | DPM           | DPM+BNF       |
| Closed-set | 2 . 56 ± . 10 | 1 . 87 ± . 09 | 1 . 88 ± . 10 | 2 . 35 ± . 14 | 3 . 58 ± . 07 |
| Open-set   | 2 . 02 ± . 09 | 1 . 82 ± . 09 | 1 . 92 ± . 11 | 2 . 67 ± . 10 | 3 . 67 ± . 06 |

BNF-conditioned version of VoiceGrad performed better than PPG-VC in audio quality and better than StarGAN-VC in speaker similarity. These were consistent with the results of the quantitative evaluation described in the previous section. The version without BNF conditioning performed reasonably well, with better audio quality than StarGAN-VC, though not as good as PPG-VC, and better speaker similarity than PPGVC, though slightly less than StarGAN-VC. However, it was only in the closed-set condition that the version without BNF conditioning was not as good as StarGAN-VC in terms of speaker similarity, and in the open-set condition it performed better than StarGAN-VC. Comparing the results of both tests under closed-set and open-set conditions, all the methods performed similarly in the closed and open set conditions, with the exception of StarGAN-VC in the speaker similarity test.

It is noteworthy that while BNF conditioning demonstrated only a modest effect of 0.2 to 0.4 in the quantitative evaluation of pMOS, it yielded a more substantial impact of 0.5 to 0.7 in the subjective MOS evaluation. This divergence in results is anticipated, as the MOS predictor likely emphasizes the assessment of audio quality (naturalness as a speech waveform) over intelligibility (the accuracy of phoneme pronunciation). Conversely, participants in the subjective listening test likely took into account both audio quality and intelligibility when evaluating the naturalness of the stimuli.

In summary, these results indicate from subjective listening tests that VoiceGrad (1) outperforms the tested baselines in both audio quality and speaker similarity, (2) is greatly improved by BNF conditioning, and (3) works as well for unknown speaker input as for known speaker input.

Audio examples of these methods are provided at [77].

## VII. CONCLUSION

In this paper, we proposed VoiceGrad, a non-parallel any-tomany VC method based upon the concepts of score matching, Langevin dynamics, and DPMs: The idea involves training a score approximator, a fully convolutional network with a U-Net structure, to predict the gradient of the log density of the mel-spectrograms of multiple speakers. Once the network is trained, it can be used to perform VC through annealed Langevin dynamics or reverse diffusion process to iteratively update the mel-spectrogram of input speech to sound like a target speaker's voice. Through objective and subjective experiments, VoiceGrad has demonstrated superior performance to the tested baselines in terms of audio quality and speaker similarity under both closed-set and open-set conditions. Additionally, we have found that the concept of BNF conditioning significantly enhances the intelligibility of the generated speech.

## APPENDIX

<!-- formula-not-decoded -->

<!-- formula-not-decoded -->

where the third line follows from the Leibniz integral rule, the fifth line follows from the chain rule, and the sixth line follows from the Bayes rule.

## REFERENCES

- [1] A. Kain and M. W. Macon, 'Spectral voice conversion for text-to-speech synthesis,' in Proc. International Conference on Acoustics, Speech, and Signal Processing (ICASSP) , 1998, pp. 285-288.
- [2] A. B. Kain, J.-P. Hosom, X. Niu, J. P. van Santen, M. Fried-Oken, and J. Staehely, 'Improving the intelligibility of dysarthric speech,' Speech Communication , vol. 49, no. 9, pp. 743-759, 2007.
- [3] K. Nakamura, T. Toda, H. Saruwatari, and K. Shikano, 'Speakingaid systems using GMM-based voice conversion for electrolaryngeal speech,' Speech Communication , vol. 54, no. 1, pp. 134-146, 2012.
- [4] Z. Inanoglu and S. Young, 'Data-driven emotion conversion in spoken English,' Speech Communication , vol. 51, no. 3, pp. 268-283, 2009.
- [5] O. T¨ urk and M. Schr¨ oder, 'Evaluation of expressive speech synthesis with voice conversion and copy resynthesis techniques,' IEEE Transactions on Audio, Speech, and Language Processing , vol. 18, no. 5, pp. 965-973, 2010.
- [6] T. Toda, M. Nakagiri, and K. Shikano, 'Statistical voice conversion techniques for body-conducted unvoiced speech enhancement,' IEEE Transactions on Audio, Speech, and Language Processing , vol. 20, no. 9, pp. 2505-2517, 2012.
- [7] P. Jax and P. Vary, 'Artificial bandwidth extension of speech signals using MMSE estimation based on a hidden Markov model,' in Proc. International Conference on Acoustics, Speech, and Signal Processing (ICASSP) , 2003, pp. 680-683.

- [8] D. Felps, H. Bortfeld, and R. Gutierrez-Osuna, 'Foreign accent conversion in computer assisted pronunciation training,' Speech Communication , vol. 51, no. 10, pp. 920-932, 2009.
- [9] D. P. Kingma and M. Welling, 'Auto-encoding variational Bayes,' in Proc. International Conference on Learning Representations (ICLR) , 2014.
- [10] D. P. Kingma, D. J. Rezendey, S. Mohamedy, and M. Welling, 'Semisupervised learning with deep generative models,' in Adv. Neural Information Processing Systems (NIPS) , 2014, pp. 3581-3589.
- [11] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio, 'Generative adversarial nets,' in Adv. Neural Information Processing Systems (NIPS) , 2014, pp. 26722680.
- [12] L. Dinh, D. Krueger, and Y. Bengio, 'NICE: Non-linear independent components estimation,' in Proc. International Conference on Learning Representations (ICLR) , 2015.
- [13] L. Dinh, J. Sohl-Dickstein, and S. Bengio, 'Density estimation using real NVP,' in Proc. International Conference on Learning Representations (ICLR) , 2017.
- [14] D. P. Kingma and P. Dhariwal, 'Glow: Generative flow with invertible 1x1 convolutions,' in Advances in Neural Information Processing Systems 31 , 2018, pp. 10 215-10 224.
- [15] C.-C. Hsu, H.-T. Hwang, Y.-C. Wu, Y. Tsao, and H.-M. Wang, 'Voice conversion from non-parallel corpora using variational auto-encoder,' in Proc. Asia-Pacific Signal and Information Processing Association Annual Summit and Conference (APSIPA ASC) , 2016, pp. 1-6.
- [16] C.-C. Hsu, H.-T. Hwang, Y.-C. Wu, Y. Tsao, and H.-M. Wang, 'Voice conversion from unaligned corpora using variational autoencoding Wasserstein generative adversarial networks,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , 2017, pp. 3364-3368.
- [17] A. van den Oord and O. Vinyals, 'Neural discrete representation learning,' in Adv. Neural Information Processing Systems (NIPS) , 2017, pp. 6309-6318.
- [18] W.-C. Huang, H.-T. Hwang, Y.-H. Peng, Y. Tsao, and H.-M. Wang, 'Voice conversion based on cross-domain features using variational auto encoders,' in Proc. International Symposium on Chinese Spoken Language Processing (ISCSLP) , 2018, pp. 165-169.
- [19] Y. Saito, Y. Ijima, K. Nishida, and S. Takamichi, 'Non-parallel voice conversion using variational autoencoders conditioned by phonetic posteriorgrams and d-vectors,' in Proc. International Conference on Acoustics, Speech, and Signal Processing (ICASSP) , 2018, pp. 52745278.
- [20] H. Kameoka, T. Kaneko, K. Tanaka, and N. Hojo, 'ACVAE-VC: Non-parallel voice conversion with auxiliary classifier variational autoencoder,' IEEE/ACM Transactions on Audio, Speech, and Language Processing , vol. 27, no. 9, pp. 1432-1443, 2019.
- [21] K. Qian, Y. Zhang, S. Chang, X. Yang, and M. Hasegawa-Johnson, 'AutoVC: Zero-shot voice style transfer with only autoencoder loss,' in Proc. International Conference on Machine Learning (ICML) , 2019, pp. 5210-5219.
- [22] L. Wan, Q. Wang, A. Papir, and I. L. Moreno, 'Generalized end-toend loss for speaker verification,' in Proc. 2018 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP) , 2018, pp. 4879-4883.
- [23] T. Kaneko and H. Kameoka, 'CycleGAN-VC: Non-parallel voice conversion using cycle-consistent adversarial networks,' in Proc. European Signal Processing Conference (EUSIPCO) , 2018, pp. 2100-2104.
- [24] T. Kaneko, H. Kameoka, K. Tanaka, and N. Hojo, 'CycleGAN-VC2: Improved cyclegan-based non-parallel voice conversion,' in Proc. International Conference on Acoustics, Speech, and Signal Processing (ICASSP) , 2019, pp. 6820-6824.
- [25] J.-Y. Zhu, T. Park, P. Isola, and A. A. Efros, 'Unpaired image-toimage translation using cycle-consistent adversarial networks,' in Proc. International Conference on Computer Vision (ICCV) , 2017, pp. 22232232.
- [26] T. Kim, M. Cha, H. Kim, J. K. Lee, and J. Kim, 'Learning to discover cross-domain relations with generative adversarial networks,' in Proc. International Conference on Machine Learning (ICML) , 2017, pp. 18571865.
- [27] Z. Yi, H. Zhang, P. Tan, and M. Gong, 'DualGAN: Unsupervised dual learning for image-to-image translation,' in Proc. International Conference on Computer Vision (ICCV) , 2017, pp. 2849-2857.
- [28] P. L. Tobing, Y.-C. Wu, T. Hayashi, K. Kobayashi, and T. Toda, 'Non-parallel voice conversion with cyclic variational autoencoder,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , 2019, pp. 674-678.
- [29] H. Kameoka, T. Kaneko, K. Tanaka, and N. Hojo, 'StarGAN-VC: Nonparallel many-to-many voice conversion using star generative adversarial networks,' in Proc. IEEE Spoken Language Technology Workshop (SLT) , 2018, pp. 266-273.
- [30] T. Kaneko, H. Kameoka, K. Tanaka, and N. Hojo, 'StarGAN-VC2: Rethinking conditional methods for stargan-based voice conversion,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , 2019, pp. 679-683.
- [31] H. Kameoka, T. Kaneko, K. Tanaka, and N. Hojo, 'Nonparallel voice conversion with augmented classifier star generative adversarial networks,' IEEE/ACM Transactions on Audio, Speech, and Language Processing , vol. 28, pp. 2982-2995, 2020.
- [32] Y. Choi, M. Choi, M. Kim, J.-W. Ha, S. Kim, and J. Choo, 'StarGAN: Unified generative adversarial networks for multi-domain image-toimage translation,' arXiv:1711.09020 [cs.CV] , Nov. 2017.
- [33] J. Serr` a, S. Pascual, and C. Segura, 'Blow: a single-scale hyperconditioned flow for non-parallel raw-audio voice conversion,' arXiv:1906.00794 [cs.LG] , Jun. 2019.
- [34] K. Tanaka, H. Kameoka, T. Kaneko, and N. Hojo, 'AttS2S-VC: Sequence-to-sequence voice conversion with attention and context preservation mechanisms,' in Proc. International Conference on Acoustics, Speech, and Signal Processing (ICASSP) , 2019, pp. 6805-6809.
- [35] H. Kameoka, K. Tanaka, D. Kwa´ sny, and N. Hojo, 'ConvS2S-VC: Fully convolutional sequence-to-sequence voice conversion,' IEEE/ACM Transactions on Audio, Speech, and Language Processing , vol. 28, pp. 1849-1863, 2020.
- [36] W.-C. Huang, T. Hayashi, Y.-C. Wu, H. Kameoka, and T. Toda, 'Voice transformer network: Sequence-to-sequence voice conversion using transformer with text-to-speech pretraining,' arXiv:1912.06813 [eess.AS] , Dec. 2019.
- [37] H. Kameoka, W.-C. Huang, K. Tanaka, T. Kaneko, N. Hojo, and T. Toda, 'Many-to-many voice transformer network,' arXiv:2005.08445 [eess.AS] , 2020.
- [38] H. Zheng, W. Cai, T. Zhou, S. Zhang, and M. Li, 'Text-independent voice conversion using deep neural network based phonetic level features,' in Proc. International Conference on Pattern Recognition (ICPR) , 2016, pp. 2872-2877.
- [39] L. Sun, K. Li, H. Wang, S. Kang, and H. Meng, 'Phonetic posteriorgrams for many-to-one voice conversion without parallel data training,' in 2016 IEEE International Conference on Multimedia and Expo (ICME) , 2016, pp. 1-6.
- [40] H. Miyoshi, Y. Saito, S. Takamichi, and H. Saruwatari, 'Voice conversion using sequence-to-sequence learning of context posterior probabilities,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , 2017, pp. 1268-1272.
- [41] L.-J. Liu, Z.-H. Ling, Y. Jiang, M. Zhou, and L.-R. Dai, 'WaveNet vocoder with limited training data for voice conversion,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , 2018, pp. 1983-1987.
- [42] S. Liu, J. Zhong, L. Sun, X. Wu, X. Liu, and H. Meng, 'Voice conversion across arbitrary speakers based on a single target-speaker utterance,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , 2018, pp. 496-500.
- [43] J.-X. Zhang, Z.-H. Ling, and L.-R. Dai, 'Non-parallel sequence-tosequence voice conversion with disentangled linguistic and speaker representations,' arXiv:1906.10508 [eess.AS] , 2019.
- [44] J.-X. Zhang, Z.-H. Ling, and L.-R. Dai, 'Recognition-synthesis based non-parallel voice conversion with adversarial learning,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , 2020, pp. 771-775.
- [45] S. Liu, Y. Cao, D. Wang, X. Wu, X. Liu, and H. Meng, 'Any-to-many voice conversion with location-relative sequence-to-sequence modeling,' IEEE/ACM Transactions on Audio, Speech, and Language Processing , vol. 29, pp. 1717-1728, 2021.
- [46] Y. Zhao, W.-C. Huang, X. Tian, J. Yamagishi, R. K. Das, T. Kinnunen, Z. Ling, and T. Toda, 'Voice Conversion Challenge 2020: Intra-lingual semi-parallel and cross-lingual voice conversion,' in Proc. Joint workshop for the Blizzard Challenge and Voice Conversion Challenge , 2020, pp. 80-98.
- [47] Y. Song and S. Ermon, 'Generative modeling by estimating gradients of the data distribution,' in Adv. Neural Information Processing Systems (NeurIPS) , 2019, pp. 11 918-11 930.
- [48] Y. Song and S. Ermon, 'Improved techniques for training score-based generative models,' arXiv:2006.09011 [cs.LG] , 2020.
- [49] J. Ho, A. Jain, and P. Abbeel, 'Denoising diffusion probabilistic models,' in Advances in Neural Information Processing Systems (NeurIPS) , vol. 33, 2020, pp. 6840-6851.

- [50] A. Q. Nichol and P. Dhariwal, 'Improved denoising diffusion probabilistic models,' in Proceedings of the 38th International Conference on Machine Learning , vol. 139, 2021, pp. 8162-8171.
- [51] A. Hyv¨ arinen, 'Estimation of non-normalized statistical models using score matching,' Journal of Machine Learning Research , vol. 6, pp. 695-709, 2005.
- [52] P. Vincent, 'A connection between score matching and denoising autoencoders.' Neural Computation , vol. 23, no. 7, pp. 1661-1674, 2011.
- [53] N. Chen, Y. Zhang, H. Zen, R. J. Weiss, M. Norouzi, and W. Chan, 'WaveGrad: Estimating gradients for waveform generation,' arXiv:2009.00713 [eess.AS] , 2020.
- [54] S. Liu, Y. Cao, D. Su, and H. Meng, 'DiffSVC: A diffusion probabilistic model for singing voice conversion,' in IEEE Automatic Speech Recognition and Understanding Workshop (ASRU) , 2021, pp. 741-748.
- [55] V. Popov, I. Vovk, V. Gogoryan, T. Sadekova, M. Kudinov, and J. Wei, 'Diffusion-based voice conversion with fast maximum likelihood sampling scheme,' in Proc. International Conference on Learning Representations (ICLR) , 2022.
- [56] H. Kameoka, T. Kaneko, K. Tanaka, N. Hojo, and S. Seki, 'VoiceGrad: Non-parallel any-to-many voice conversion with annealed langevin dynamics,' arXiv:2010.02977 [cs.SD] , 2020.
- [57] J. Kong, J. Kim, and J. Bae, 'HiFi-GAN: Generative adversarial networks for efficient and high fidelity speech synthesis,' in Advances in Neural Information Processing Systems (NeurIPS) , 2020, pp. 1702217 033.
- [58] S. Kim, T. Hori, and S. Watanabe, 'Joint CTC-attention based endto-end speech recognition using multi-task learning,' in Proc. IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP) , 2017, pp. 4835-4839.
- [59] O. Ronneberger, P. Fischer, and T. Brox, 'U-Net: Convolutional networks for biomedical image segmentation,' arXiv:1505.04597 [cs.CV] , 2015.
- [60] Y. N. Dauphin, A. Fan, M. Auli, and D. Grangier, 'Language modeling with gated convolutional networks,' in Proc. International Conference on Machine Learning (ICML) , 2017, pp. 933-941.
- [61] X. Glorot and Y. Bengio, 'Understanding the difficulty of training deep feedforward neural networks,' in Proc. the 13th International Conference on Artificial Intelligence and Statistics , 2010, pp. 249-256.
- [62] T. Salimans and D. P. Kingma, 'Weight normalization: A simple reparameterization to accelerate training of deep neural networks,' in Advances in Neural Information Processing Systems (NIPS) , D. Lee, M. Sugiyama, U. Luxburg, I. Guyon, and R. Garnett, Eds., vol. 29. Curran Associates, Inc., 2016. [Online]. Available: https://proceedings.neurips.cc/paper/2016/file/ed265bc903a5a097f61d3ec064d96d2e-Paper.pdf
- [63] J. Kominek and A. W. Black, 'The CMU Arctic speech databases,' in Proc. ISCA Speech Synthesis Workshop (SSW) , 2004, pp. 223-224.
- [64] https://github.com/auspicious3000/autovc.
- [65] https://github.com/liusongxiang/ppg-vc.
- [66] https://github.com/kamepong/StarGAN-VC.
- [67] M. Arjovsky and L. Bottou, 'Towards principled methods for training generative adversarial networks,' in Proc. International Conference on Learning Representations (ICLR) , 2017.
- [68] I. Gulrajani, F. Ahmed, M. Arjovsky, V. Dumoulin, and A. Courville, 'Improved training of Wasserstein GANs,' in Adv. Neural Information Processing Systems (NIPS) , 2017, pp. 5769-5779.
- [69] Y. Choi, M. Choi, M. Kim, J.-W. Ha, S. Kim, and J. Choo, 'StarGAN: Unified generative adversarial networks for multi-domain image-toimage translation,' in Proc. IEEE Conference on Computer Vision and Pattern Recognition , vol. 1711, 2018.
- [70] http://www.kecl.ntt.co.jp/people/kameoka.hirokazu/Demos/stargan-vc2/.
- [71] D. Kingma and J. Ba, 'Adam: A method for stochastic optimization,' in Proc. International Conference on Learning Representations (ICLR) , 2015.
- [72] A. Baevski, Y. Zhou, A. Mohamed, and M. Auli, 'wav2vec 2.0: A framework for self-supervised learning of speech representations,' in Advances in Neural Information Processing Systems , vol. 33, 2020, pp. 12 449-12 460.
- [73] J. Kahn, M. Rivi` ere, W. Zheng, E. Kharitonov, Q. Xu, P. Mazar´ e, J. Karadayi, V. Liptchinsky, R. Collobert, C. Fuegen, T. Likhomanenko, G. Synnaeve, A. Joulin, A. Mohamed, and E. Dupoux, 'Libri-Light: A benchmark for ASR with limited or no supervision,' in Proc. International Conference on Acoustics, Speech and Signal Processing (ICASSP) , 2020, pp. 7669-7673.
- [74] V. Panayotov, G. Chen, D. Povey, and S. Khudanpur, 'Librispeech: An ASR corpus based on public domain audio books,' in Proc. IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP) , 2015, pp. 5206-5210.
- [75] T. Saeki, D. Xin, W. Nakata, T. Koriyama, S. Takamichi, and H. Saruwatari, 'UTMOS: UTokyo-SaruLab system for VoiceMOS Challenge 2022,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , vol. 4521-4525, 2022.
- [76] W. C. Huang, E. Cooper, Y. Tsao, H.-M. Wang, T. Toda, and J. Yamagishi, 'The VoiceMOS Challenge 2022,' in Proc. Annual Conference of the International Speech Communication Association (Interspeech) , 2022, pp. 4536-4540.
- [77] http://www.kecl.ntt.co.jp/people/kameoka.hirokazu/Demos/voicegrad2/.