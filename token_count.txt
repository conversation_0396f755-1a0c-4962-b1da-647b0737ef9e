Recent advancements in Natural Language Processing (NLP) have been significantly driven by instruction-tuning of language models (<PERSON><PERSON><PERSON> et al., 2022).  While scaling models is important for emergent properties (<PERSON> et al., 2020), the quality of training data is equally vital (<PERSON> et al., 2023; <PERSON> et al., 2024). This is true during pretraining and instruction-tuning. Instruction-tuning aims to align model outputs with human expectations across various tasks (<PERSON><PERSON><PERSON> et al., 2022), achieved through methods like supervised fine-tuning and reinforcement learning (<PERSON> et al., 2023a). Alignment involves ensuring helpfulness and accurate responses while avoiding harmful topics (<PERSON> et al., 2022), balancing helpfulness with safety alignment regarding toxicity and bias, similar to content moderation (<PERSON> et al., 2024).  Aligning language models for helpfulness is crucial, but the ability to disengage from certain topics is also essential for real-world applications.  We introduce the concept of 'topic-following,' combining instruction-tuning and moderation. This involves defining complex instructions dictating how an intelligent assistant (chatbot) should interact with users, including examples of appropriate interactions and scenarios where the user deviates (distractors). In these cases, the chatbot should decline to respond or guide the conversation back to the relevant topic.  Topic-following instructions define when an assistant should respond or steer away during extended dialogues. Unlike safety alignment, which uses predefined behaviors, topic-following uses user-defined moderation, similar to programmable guardrails (<PERSON><PERSON><PERSON> et al., 2023), but using natural language instead of a domain-specific language.  As a first effort, we present CANTTALKABOUTTHIS, a dataset of 1080 synthetic dialogues designed to train models to stay on topic. Our creation process involves generating topic-following prompts, creating dialogues adhering to these instructions (using dialogue inpainting, Dai et al., 2022), and integrating distractors to test topic-following.  Our findings show that even state-of-the-art LLMs struggle with staying on topic, often engaging with distractors.  Fine-tuning models on our dataset significantly improves performance in following complex dialogue instructions.  However, our synthetic distractors are simplistic.  To address this, we developed an additional dataset with human-crafted distractors.  We believe topic-following is a crucial, overlooked aspect of instruction-tuning (Kopf et al., 2024; Longpre et al., 2023). Integrating CANTTALKABOUTTHIS enhances an LLM's ability to comprehend and follow complex system instructions about interaction topics, particularly advantageous for task-oriented dialogue systems (TODS) (Chen et al., 2017) like OpenAI's Custom GPTs (OpenAI, 2023).  Our approach has two side benefits: improved instruction-following and comparable efficacy to LLAMAGUARD (Inan et al., 2023) in safety alignment, even zero-shot.  Our work highlights the necessity of topic-following in instruction-tuning, introduces a method for developing topic-following datasets, and releases CANTTALKABOUTTHIS, showing that training on topic-following data enhances an LLM's ability to navigate away from distractors, improve instruction following, and perform effective zero-shot safety alignment.