# Extracted content

arXiv:2010.02977v3  [cs.SD]  9 Mar 20241
V oiceGrad: Non-Parallel Any-to-Many V oice
Conversion with Annealed Langevin Dynamics
<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON> —In this paper, we propose a non-parallel any-to-
many voice conversion (VC) method termed VoiceGrad . Inspired
by WaveGrad, a recently introduced novel waveform generati on
method, VoiceGrad is based upon the concepts of score matchi ng,
Langevin dynamics, and diffusion models. The idea involves
training a score approximator, a fully convolutional netwo rk with
a U-Net structure, to predict the gradient of the log density of the
speech feature sequences of multiple speakers. The trained score
approximator can be used to perform VC by using annealed
Langevin dynamics or reverse diffusion process to iterativ ely
update an input feature sequence towards the nearest statio nary
point of the target distribution. Thanks to the nature of thi s
concept, VoiceGrad enables any-to-many VC, a VC scenario in
which the speaker of input speech can be arbitrary, and allow s
for non-parallel training, which requires no parallel utte rances.
Index Terms —Voice conversion (VC), non-parallel VC, any-to-
many VC, score matching, Lange<PERSON> dynamics, diffusion mode ls.
I. I NTRODUCTION
V oice conversion (VC) is a technique to convert the voice of
a source speaker to another voice without changing the utter ed
sentence. Its applications range from speaker-identity mo diﬁ-
cation [1] to speaking assistance [2], [3], speech enhancem ent
[4]–[6], bandwidth extension [7], and accent conversion [8 ].
While many conventional VC methods require parallel
utterances to train acoustic models for feature mapping, non-
parallel VC methods are ones that can work without parallel
utterances or transcriptions for model training. These met hods
can be useful in many cases since constructing a parallel
corpus is often very costly and non-scalable. Another poten -
tially important requirement for VC methods is the ability t o
achieve any-to-many conversion, namely to convert speech
of an arbitrary speaker to the voices of multiple speakers.
Such methods are also attractive in that they can work for
input speech of unknown speakers without model retraining
or adaptation.
A number of non-parallel methods have already been
proposed, among which those that have attracted particular
attention in recent years are based on deep generative model s,
such as variational autoencoders (V AEs) [9], [10], generat ive
adversarial networks (GANs) [11], and ﬂow-based models
[12]–[14].
V AEs are a stochastic version of autoencoders (AEs), con-
sisting of an encoder and decoder. The encoder and decoder
are modeled as different neural networks that produce a set o f
H. Kameoka, T. Kaneko, K. Tanaka, N. Hojo, and S. Seki are
with NTT Communication Science Laboratories, Nippon Teleg raph and
Telephone Corporation, Atsugi, Kanagawa, 243-0198 Japan ( e-mail: hi-
<EMAIL>).parameters of parametric distributions, such as Gaussians . The
decoder represents the conditional distribution of a given set
of data conditioned on a latent variable, whereas the encode r
represents the posterior distribution of the latent variab le.
In V AEs, the encoder and decoder networks are trained to
maximize the variational lower bound of the marginal log-
likelihood described by these distributions. In VC methods
based on V AEs [15]–[20], the encoder is responsible for
converting the acoustic features of input speech into laten t
variables, while the decoder is responsible for doing the
opposite. The basic idea is to condition the decoder on a
target speaker code along with the latent variables so that
the decoder can learn to generate acoustic features that are
likely to be produced by the corresponding speaker and be
linguistically consistent with the input speech. Hence, th ese
methods are capable of simultaneously learning mappings to
multiple speakers’ voices by using a single pair of encoder
and decoder networks. By intentionally not conditioning th e
encoder on a source speaker code, the encoder can be trained
to work in a speaker-independent manner. Under this setting ,
these methods allow for any-to-many conversions. Subseque nt
to these methods, a regular (non-variational) AE-based met hod
called AutoVC was proposed [21] and proved to be capable of
handling any-to-any conversions by having the decoder take as
input the speaker embeddings obtained with a speaker encode r
pretrained using the generalized end-to-end loss [22].
GANs provide a general framework for training a generator
network without an explicit deﬁnition of the generator dist ribu-
tion. The goal is to train a generator network so as to deceive
a discriminator network, which learns to distinguish fake d ata
generated by the generator from real data. The training proc ess
in GANs is formulated as a minimax game using an adversarial
loss, in such a way that the generator progressively gets
better at generating data that appear to be real, while the
discriminator gets better at distinguishing them as fake da ta.
The minimax game using the adversarial loss is shown to
be equivalent to a process of ﬁtting the implicitly deﬁned
generator distribution to the data distribution. We previo usly
reported a non-parallel VC method [23], [24] using a GAN
variant called cycle-consistent GAN (CycleGAN) [25]–[27] ,
which was originally proposed as a method for translating
images with unpaired training examples. The idea is to train
a pair of mappings between one speaker’s voice and another
speaker’s voice using a cycle-consistency loss along with t he
adversarial loss. While the adversarial loss is used to ensu re
that the output of each mapping will follow the correspondin g
target distribution, the cycle-consistency loss is introd uced to
ensure that converting input speech into another speaker’s

2
voice and converting it back to the original speaker’s voice
will result in the original input speech. This encourages ea ch
mapping to make only a minimal change from the input so as
not to destroy the linguistic content of the input. The cycle -
consistency loss has recently proved effective also in V AE-
based methods [28]. Although the CycleGAN-based method
was found to work reasonably well, one limitation is that it
can only handle one-to-one conversions. To overcome this
limitation, we further proposed an improved version [29]–
[31] based on another GAN variant called StarGAN [32],
which offers the advantages of V AE-based and CycleGAN-
based methods concurrently. As with V AE-based methods,
this method is capable of simultaneously learning mappings
to multiple speakers’ voices using a single network and thus
can fully use available training data collected from multip le
speakers. In addition, it works without source speaker info r-
mation, and can thus handle any-to-many conversions.
Flow-based models are a class of generative models con-
sisting of multiple invertible nonlinear layers called ﬂow s.
Flows can be seen as a series of changes of variables, which
gradually transform each real data sample into a random nois e
sample following some prespeciﬁed distribution. The basic
idea is to enable the direct evaluation and optimization of a
log-likelihood function, which is usually hard to compute, by
using a special network architecture consisting of ﬂows who se
Jacobians and inverse functions are easy to compute. Recent ly,
a non-parallel VC method using ﬂow-based models has been
proposed [33]. The principle is conceptually similar to V AE -
based methods in the sense that the forward and inverse ﬂows
play similar roles as the encoder and decoder in a V AE.
Several VC methods based on sequence-to-sequence (S2S)
models have also been proposed, including the ones we
proposed previously [34]–[37]. While this approach usuall y
requires parallel corpora for training, the recognition-s ynthesis
approach [38]–[45], in which an automatic speech recogniti on
(ASR) model and a decoder are cascaded to perform VC,
allows for nonparallel training by separately training the ASR
model and decoder using text or phoneme transcripts. In this
approach, the ASR model is trained to extract a sequence of
linguistic-related features, e.g., phonetic posteriorgr am (PPG)
or a sequence of bottleneck features (BNFs) from source
speech, whereas the decoder is trained to generate speech of
a target speaker from that sequence. It should be noted that
the top-performing systems in V oice Conversion Challenge
(VCC) 2020 [46] adopted the PPG-based recognition-synthes is
approach.
Score-based generative models [47], [48] or diffusion prob -
abilistic models [49], [50] are another notable class of gen era-
tive models, different from the above, that have recently be en
proven to be very effective in generating images and speech
waveforms. Inspired by the success of these models, in this
paper we propose yet another method for non-parallel any-to -
many VC based on the concepts of Langevin dynamics and
reverse diffusion, and compare it objectively and subjecti vely
with the conventional methods. The proposed model uses a
neural network in a way that the behavior depends less on the
distribution of inputs, which we expect to be advantageous i n
any-to-one or any-to-many VC tasks, especially under low-resource conditions. Another motivation for adopting a sco re-
based generative model or DPM for VC is the ﬂexibility
to customize the conversion process to meet various user
requirements. We anticipate achieving this by combining in de-
pendently pretrained classiﬁers or other types of score-ba sed
models to adjust the update direction at each time step of the
Langevin dynamics or reverse diffusion process. This aspec t
is particularly appealing because it allows for customizat ion
without requiring retraining.
II. S CORE MATCHING AND LENGEVIN DYNAMICS
We start by brieﬂy reviewing the fundamentals of the
score-based generative models, i.e., the concepts of Lange vin
dynamics and score matching.
For any continuously differentiable probability density p(x),
we call∇xlogp(x) =∂logp(x)
∂xitsscore function [51]. If we
are given the score function of the data of interest, we can us e
Langevin dynamics to draw samples from the corresponding
distribution: Starting from an initial point x, we can iteratively
reﬁne it in a noisy gradient ascent fashion so that the log-
densitylogp(x)will be increased
x←x+γ∇xlogp(x)+/radicalbig
2γz, (1)
whereγ >0is a step size and zis a zero-mean Gaussian
white noise with variance 1 that is drawn independently at
each iteration. It can be shown that when γis sufﬁciently small
and the number of iterations is sufﬁciently large, xwill be
an exact sample from p(x)under some regularity conditions.
This idea is particularly attractive in that we only need to
access∇xlogp(x)instead ofp(x), which is usually very hard
to estimate. Hence, given a set of training examples X=
{xn}1≤n≤N, the focus is on how to estimate the score function
fromXat hand.
Score matching [51] is a method to estimate the score func-
tion of the true distribution by optimizing a score approxim ator
sθ(x)parameterized by θ. We can use the expected squared
error between sθ(x)and∇xlogp(x)
E(θ) =Ex∼p(x)/bracketleftBig
/ba∇dblsθ(x)−∇xlogp(x)/ba∇dbl2
2/bracketrightBig
, (2)
as the objective function to be minimized with respect to θ.
Here,Ex∼p(x)[·]can be approximated as the sample mean
overX. Even when the regression target ∇xlogp(x)is not
directly accessible, there are several ways to make this pro blem
tractable without requiring an explicit expression of p(x). One
is implicit score matching [51], which uses the fact that (2) is
equivalent up to a constant to
I(θ) =Ex∼p(x)/bracketleftbig
2tr(∇xsθ(x))+/ba∇dblsθ(x)/ba∇dbl2
2/bracketrightbig
, (3)
where∇xsθ(x)denotes the Jacobian of sθ(x), andtr(·)
denotes the trace of a matrix. However, unfortunately, comp ut-
ingtr(∇xsθ(x))can be extremely expensive when sθ(x)is
expressed as a deep neural network and xis high dimensional.
Another technique involves denoising score matching (DSM)
[52], which is noteworthy in that it can completely circumve nt
tr(∇xsθ(x)). The idea is to ﬁrst perturb xin accordance with
a pre-speciﬁed noise distribution qσ(˜x|x)parameterized by
σand then estimate the score of the distribution qσ(˜x) =

3
/integraltextqσ(˜x|x)p(x)dxof the perturbed version. It should be noted
thatqσ(x)can be seen as a Parzen window density estimator
forp(x). If we assume the noise distribution to be Gaussian
qσ(˜x|x) =N(˜x;x,σ2I), the loss function to be minimized
becomes
Dσ(θ) =Ex∼p(x),˜x∼N(˜x;x,σ2I)/bracketleftBigg/vextenddouble/vextenddouble/vextenddouble/vextenddoublesθ(˜x)−x−˜x
σ2/vextenddouble/vextenddouble/vextenddouble/vextenddouble2
2/bracketrightBigg
.(4)
As shown in reference [52], the optimal sθ(x)that mini-
mizes (4) almost surely equals ∇xlogqσ(x), and it matches
∇xlogp(x)when the noise variance σ2is small enough such
thatqσ(x)≈p(x). The underlying intuition is that the gradient
of the log density at some perturbed point ˜xshould be directed
towards the original clean sample x.
Recently, attempts have been made to apply the DSM
principle to image generation [47], [48] by using a neural
network to describe the score approximator sθ(x). A major
challenge to overcome in applying DSM to image generation
tasks is that real-world data including images tend to resid e on
a low dimensional manifold embedded in a high-dimensional
space. This can be problematic in naive applications of DSM
since the idea of DSM is valid only when the support of the
data distribution is the whole space. In practice, the scarc ity
of data in low-density regions can cause difﬁculties in both
score matching-based training and Langevin dynamics-base d
test sampling. To overcome this obstacle, the authors of [47 ]
proposed a DSM variant called weighted DSM . The idea is
to use multiple noise levels {σl}L
l=1in both training and test
sampling. During test sampling, the noise level is graduall y
decreased so that qσl(x)can initially ﬁll the whole space and
eventually converge to the true distribution p(x). To let the
score approximator learn to behave differently in accordan ce
with the different noise levels, they proposed using a noise
conditional network to describe sθ(x,l), which takes the noise
level indexlas an additional input. For the training objective,
they proposed using a weighted sum of Dσ1(θ),...,DσL(θ)
LDSM(θ) =1
LL/summationdisplay
l=1λlDσl(θ), (5)
whereλl>0is a positive constant that can be chosen
arbitrarily. Based on their observation that when sθ(x,l)is
trained to optimality, /ba∇dblsθ(x,l)/ba∇dbl2tends to be proportional to
1/σl, they recommended setting λlatσ2
l, which results in
LDSM(θ) =1
LL/summationdisplay
l=1Ex,˜x/bracketleftBigg/vextenddouble/vextenddouble/vextenddouble/vextenddoubleσlsθ(˜x,l)−x−˜x
σl/vextenddouble/vextenddouble/vextenddouble/vextenddouble2
2/bracketrightBigg
,(6)
where the expectation is taken over the training examples of x
and the random samples of ˜x∼N(˜x;x,σ2
lI). Note that they
also recommended setting {σl}1≤l≤Lto a positive geometric
sequence such thatσ2
σ1=···=σL
σL−1∈[0,1]. Oncesθ(x,l)is
trained under these settings, one can produce a sample from
qσL(x)via an annealed version of Langevin dynamics with a
special step size schedule such that γl=ε·σ2
l/σ2
Lfor thelth
noise level, where εis a scaling factor (Algorithm 1).Algorithm 1 Annealed Langevin dynamics [47]
Require:{σl}L
l=1,ε
Initialize x∼N(0,I)
forl= 1 toLdo
γl←ε·σ2
l/σ2
L
fort= 1 toTdo
Drawz∼N(0,I)
Updatex←x+γlsθ(x,l)+√2γlz
end for
end for
returnx
III. F ORMULATION AS DIFFUSION MODELS
As revealed by Ho et al. [49], DSM is closely related to
diffusion probabilistic models (DPMs), or diffusion model s for
short. Here, we review the principle of DPMs and show that
their training objective and sample generation process hav e
similarities to those of DSM described above.
Given a data sample x0, normalized to have mean zero and
unit variance, the diffusion process of DPM is deﬁned as a
Markov chain that gradually adds Gaussian noise to x0. Based
on this assumed process, the model, parametrized by θ, is
trained to ﬁnd a reverse process that gradually reconstruct sx0
from its diffused versions. Formally, DPMs are latent varia ble
models of the form
pθ(x0) =/integraldisplay
pθ(x0:L)dx1:L, (7)
where the notation xi:jis used to denote the set {xi,...,xj}
andx1,...,xLare latent variables corresponding to the dif-
fused versions of x0, withl= 1,...,L being the time step of
the diffusion process. The joint distribution q(x1:L|x0)given
x0is called the diffusion process, which is assumed to be a
Markov chain that factors as
q(x1:L|x0) =L/productdisplay
l=1q(xl|xl−1), (8)
whereq(xl|xl−1)is deﬁned as
q(xl|xl−1) =N(xl;/radicalbig
1−βlxl−1,βlI). (9)
(9) can be viewed as a process of scaling xl−1by√1−βl
and then adding Gaussian noise with variance βl. This scaling
has the role of keeping the variance of xlat unity at each time
step. It is important to note that q(xl|x0)at any time step l
can be obtained analytically as
q(xl|x0) =N(xl;√¯αlx0,(1−¯αl)I), (10)
whereαl= 1−βland¯αl=/producttextl
i=1αi, from the fact that the
sum of Gaussian random variables is also Gaussian. If we use
ǫ∼N(0,I)to denote a standard Gaussian random variable,
(10) can be rewritten as
xl=√¯αlx0+√1−¯αlǫ. (11)
The joint distribution pθ(x0:L)is called the reverse process,
which is also assumed to be a Markov chain that factors as
pθ(x0:L) =pθ(xL)L/productdisplay
l=1pθ(xl−1|xl), (12)

4
starting from xL∼N(0,I), wherepθ(xl−1|xl)is deﬁned as
pθ(xl−1|xl) =N(xl−1;µθ(xl,l),ν2
lI). (13)
Here,νlis an untrained time-dependent constant and µθ(xl,l)
is assumed to be the ouput of a deep neural network that
is parameterized by θand conditioned on l. According to
reference [49], setting ν2
ltoβlhas been experimentally found
to work well. Now, we can use the variational bound on
the negative log-likelihood E[−logpθ(x0)]as the training
objective for θto be minimized:
LDPM(θ) =Ex1:L∼q(x1:L|x0)/bracketleftbigg
logq(x1:L|x0)
pθ(x0:L)/bracketrightbigg
. (14)
It is important to note that since the exact bound of LDPM(θ)is
achieved when the Kullback-Leibler (KL) divergence betwee n
pθ(x1:L|x0)andq(x1:L|x0)is 0, minimizingLDPM(θ)with
respect toθmeans not only ﬁtting pθto the data distribution,
but also making pθandqas consistent as possible. By using
(11) and a reparameterization
µθ(xl,l) =1√αl/parenleftbigg
xl−1−αl√1−¯αlǫθ(xl,l)/parenrightbigg
, (15)
it can be shown that (14) can be rewritten as
LDPM(θ) =
1
LL/summationdisplay
l=1Ex0,ǫ[cl/ba∇dblǫθ(√¯αlx0+√
1−¯αlǫ,l)−ǫ/ba∇dbl2
2],(16)
where the expectation is taken over the training examples of x0
and the random samples of ǫ∼N(0,I), andclis a constant
related toαl,¯αl, andνl. See reference [49] for the detailed
derivation of (16). As it has been reported by Ho et al. that
better model training can be achieved by setting clto 1 [49],cl
is henceforth set to 1. The reparameterization of (15) impli es
representing ǫθ(xl,l)instead of µθ(xl,l)as a neural network.
Let us now compare (16) with (6). By using the symbol x0
instead of xand using a reparameterization ˜x=x0+σlǫ, (6)
can be rewritten as
LDSM(θ) =1
LL/summationdisplay
l=1Ex0,ǫ[/ba∇dblσlsθ(x0+σlǫ,l)+ǫ/ba∇dbl2
2],(17)
where the expectation is taken over the training examples of
x0and the random samples of ǫ∼N(0,I). A comparison of
(17) and (16) reveals that −σlsθ(x0+σlǫ,l)andǫθ(√¯αlx0+√1−¯αlǫ,l)are in correspondence with each other. Hence,
we will henceforth refer to ǫθas the score approximator as
well. The main difference is that the input to the network is
x0+σlǫin DSM, while it is√¯αlx0+√1−¯αlǫin DPM.
This means that in DSM the variance of the network input xl
is assumed to increase after the addition of Gaussian noise,
whereas in DPM the variance is assumed to remain 1 owing
to the scaling of x0.
Onceθis trained, one can produce a sample from pθ(x)
in accordance with the Markov chain of the reverse process
(Algorithm 2). A comparison of Algorithms 1 and 2 reveals the
connection between the sample generation algorithms based
on the Langevin dynamics and the reverse diffusion process.
Notice that in Algorithm 1, the loop counter lincrements fromAlgorithm 2 Reverse diffusion process
Require:{αl}L
l=1
Initialize x∼N(0,I)
forl=Lto1do
Drawz∼N(0,I)
Updatex←1√αl/parenleftBig
x−1−αl√1−¯αlǫθ(x,l)/parenrightBig
+νlz
end for
returnx
1toLin the for-loop, whereas in Algorithm 2 it decrements
fromLto1. This is just a matter of how the diffusion time
step or noise level is indexed, not an essential difference. In
fact, if we deﬁne l′=L−l+ 1 as the new loop counter in
Algorithm 1, we can rewrite Algorithm 1 to decrement the
loop counter l′fromLto1, as in Algorithm 2. Let us now
focus on the update equations in Algorithms 1 and 2. Since
∇xllogq(xl) =Ex0[∇xllogq(xl|x0)](see Appendix for the
proof) and∇xllogq(xl|x0) =−ǫ√1−¯αl, we have
∇xllogq(xl) =−ǫ√1−¯αl, (18)
whereǫ∼ N(0,I). Sinceǫθ(xl,l)is trained to predict ǫ
in (18), ifθis successfully trained, −ǫθ(xl,l)√1−¯αlshould be a
good approximation of ∇xllogq(xl). Therefore, the update
equation in Algorithm 2 can be interpreted as moving xlin the
direction of∇xllogq(xl)with step size 1−αl, then scaling by
1√αl, and ﬁnally adding νlz. Thus, this can be seen as the same
process as the update equation in Algorithm 1, except for the
scaling. This scaling corresponds to the inverse of the scal ing
forxl−1assumed in (9). Recall that −σlsθ(xl,l)in DSM
corresponds to ǫθ(xl,l)in DPM. Since sθ(xl,l)is trained
to approximate∇xllogq(xl), from (18), this implies that σl
corresponds to√1−¯αl.
IV. R ELATED WORK
As the name implies, V oiceGrad is inspired by WaveGrad
[53], a recently introduced novel neural waveform generati on
method based on the concept of DPMs. The idea is to
model the process of gradually converting a Gaussian white
noise signal into a speech waveform that is best associated
with a conditioning mel-spectrogram as the reverse diffusi on
process. After training, one can generate a waveform (in a
non-autoregressive manner, unlike WaveNet) given a mel-
spectrogram via the trained reverse diffusion process, sta rting
from a randomly drawn noise signal. One straightforward way
of adapting this idea to VC tasks would be to use the model
to generate the acoustic feature sequence of target speech a nd
that of source speech as the conditioning input. This idea
may work if time-aligned parallel utterances of a speaker pa ir
are available, but here we are concerned with achieving non-
parallel any-to-many VC, as described below.
Note that several DPM-based VC methods, such as Diff-
SVC [54] and Diff-VC [55], have been proposed after the
publication of the preprint paper on this work [56]. The idea
of Diff-VC is to ﬁrst convert an input mel-spectrogram to an
“average voice” mel-spectrogram using an encoder trained b y

5
phoneme supervision on speech samples from many speakers,
and then to a target speaker mel-spectrogram using the rever se
diffusion process of a trained DPM. As described below, our
V oiceGrad differs in that it performs the Langevin dynamics
or reverse diffusion process starting directly from the mel -
spectrogram of source speech.
V. V OICE GRAD
A. Key Idea
We will now describe how V oiceGrad uses the concepts of
DSM, Langevin dynamics, and DPMs to achieve non-parallel
any-to-many VC. Given a source speech feature sequence
(e.g., mel-spectrogram), our key idea is to formulate the VC
problem as ﬁnding the stationary point of the log density of
target speech feature sequences nearest to the source seque nce.
Thus, we can naturally think of employing the Langevin
dynamics or reverse diffusion process starting from a certa in
noise level to perform VC by using the source speech feature
sequence as an initial point and moving it along the gradient
direction of the log density of target speech feature sequen ces.
From the DPM perspective, this corresponds to assuming that
the source speech feature sequence is a diffused version of
a target speech feature sequence. Although this process doe s
not necessarily ensure the preservation of the linguistic c ontent
in source speech, it was experimentally found to work under
some settings, as detailed later. To enable a single score
approximator to predict the score functions of the feature
sequences of all the Ktarget speakers included in the training
set, we also condition the score approximator network on the
target speaker index k∈{1,...,K}. Namely, V oiceGrad uses
a network ǫθ(x,l,k)to describe the score approximator, where
xdenotes the input speech feature.
Owing to the idea described above, V oiceGrad does not
require the training set to consist of parallel utterances, allows
the speaker of input speech at test time to be arbitrary, and c an
convert input speech to the voices of multiple known speaker s
using a single trained network.
B. Acoustic Feature and Waveform Generation
We use the 80-dimensional log mel-spectrogram extracted
from input speech as the acoustic feature sequence to be
converted, and choose to use HiFi-GAN [57] for waveform
generation from the converted mel-spectrogram. At trainin g
time, each element xd,mof the log mel-spectrogram xof each
training example is normalized to xd,m←(xd,m−ψd)/ζd,
whereddenotes the mel-ﬁlterbank channel of the mel-
spectrogram, mdenotes the frame index, and ψdandζddenote
the mean and standard deviation of the d-th channel elements
of all the training samples. At test time, the mel-spectrogr am
of input speech is normalized in the same way.
C. Training and Conversion Processes
As noted above, in V oiceGrad, the generation process of
a target speaker’s mel-spectrogram is treated as a gradient
descent search for a probabilistically likely mel-spectro gram inAlgorithm 3 DSM-based V oiceGrad
Require:{σl}L
l=L′,ε,T,x,k
forl=L′toLdo
γl←ε·σ2
l/σ2
L
fort= 1 toTdo
Drawz∼N(0,I)
Updatex←x−γl
σlǫθ(x,l,k)+√2γlz
end for
end for
returnx
Algorithm 4 DPM-based V oiceGrad
Require:{αl}L′
l=1,{¯αl}L′
l=1,x,k
forl=L′to1do
Drawz∼N(0,I)
Updatex←1√αl/parenleftBig
x−1−αl√1−¯αlǫθ(x,l,k)/parenrightBig
+νlz
end for
returnx
the DSM version, whereas in the DPM version, it is regarded
as the reverse diffusion process (Fig. 1).
As in the previous study [53], using the L1measure instead
of theL2measure in (6) and (16) was found to be effective in
terms of both training stability and audio quality at test ti me.
Hence, the training objectives for V oiceGrad to be minimize d
with respect to θunder the DSM and DPM formulations
become
LDSM(θ) =E[/ba∇dblǫθ(x0+σlǫ,l,k)−ǫ/ba∇dbl1], (19)
LDPM(θ) =E[/ba∇dblǫθ(√¯αlx0+√
1−¯αlǫ,l,k)−ǫ/ba∇dbl1],(20)
respectively, where the expectations in both equations are
taken over the random samples of l∼U(1,...,L),k∼
U(1,...,K), andǫ∼ N(0,I), and the training examples
ofx0∼p(x0|k). Here,U(·)is used to denote a discrete
uniform distribution over the integers in its argument. Not e that
−σlsθ(xl,l,k)is expressed here as ǫθ(xl,l,k)in (19) to unify
the symbols for the score approximator. Given a set of traini ng
examplesX={x(k,n)
0}1≤k≤K,1≤n≤N, whereNis the num-
ber of training utterances of each speaker, x(k,n)
0∈R80×Mk,n
denote the mel-spectrogram of the nth training utterance of the
kth speaker, respectively, Mk,ndenotes the length of x(k,n)
0,
the expectation Ek,x0[·]can be approximated as the sample
mean overX, andEǫ[·]can be evaluated using the Monte
Carlo approximation.
Once the score approximator ǫθis trained using (19) or
(20) as a criterion, we can use Algorithm 3 or Algorithm 4 to
convert an input mel-spectrogram xto the voice of speaker k.
For both algorithms, we found experimentally that starting the
iteration from a certain point in the middle rather than from
the beginning is effective in terms of the audio quality of th e
converted speech. Henceforth, we use L′to denote the starting
noise level of the iteration. Fig. 2 shows an example of the
actual process of converting a male speaker’s mel-spectrog ram
to a female voice by reverse diffusion process (Algorithm 4)
starting from L′= 11 .

6
Fig. 1. Mel-spectrogram generation by reverse diffusion pr ocess
Fig. 2. Process of converting a male speaker’s mel-spectrog ram to a female voice by a reverse
diffusion process starting from level 11 at test time.
D. BNF Conditioning
In V-A, we noted that the Langevin dynamics or reverse
diffusion process, starting from the mel-spectrogram of so urce
speech, has been experimentally found to preserve the lingu is-
tic content in the source speech to a certain extent. However ,
we have also found that by designing the score approximator
to incorporate the BNF sequence obtained by an ASR model
from source speech, the conversion process can be guided to
preserve the linguistic content and improve the intelligib ility
of the generated speech.
To extract the BNF sequence from a mel-spectrogram, we
use the bottleneck feature extractor (BNE) proposed by Liu
et al. [45]. This BNE is obtained by inserting an additional
bottleneck layer between the encoder and decoder in an end-
to-end phoneme recognizer [58], training the entire networ k
on a large speech recognition data corpus, and dropping all
the layers subsequent to the bottleneck layer from the netwo rk
after training. For more details on the architecture and tra ining
scheme of the BNE, see reference [45].
The BNF sequence is expected to contain little information
other than the linguistic content of the input speech. Namel y,
the BNF sequence should remain virtually unchanged before
and after VC. Therefore, it is expected to work even if the
BNF sequence of target speech is used as an additional input
to the network during the score approximator training where as
the BNF sequence of the source speech is used instead at test
time. By using the BNF sequence in this way, we expect that
the BNF sequence will encourage the score approximator to
learn to predict the target score function using the linguis tic
information of the input speech as a guide. With the above
expectation, the BNF sequence is obtained from the mel-
spectrogram of each training example at training time and fr om
the mel-spectrogram of source speech at test time.
The current model setup is such that the output of the trainedBNE becomes a sequence of 144-dimensional BNF vectors of
the same length as the mel-spectrogram input to the BNE.
E. Noise Variance Scheduling
Although the noise variances, i.e., {σl}lin the DSM for-
mulation and{βl}lin the DPM formulation, can be set arbi-
trarily, it has been experimentally reported that the choic e of
these variances can affect sample quality in image generati on
applications. For the DSM formulation, we set {σl}1≤l≤L
at a geometric sequence, as in [47], with common ratio
σ2
σ1=···=σL
σL−1≈0.787, whereL= 21 ,σ1= 1.2, and
σL= 0.01. For the DPM formulation, we use a cosine-based
schedule [50] for the noise variance setting. Speciﬁcally, we
construct a schedule in terms of ¯αl(instead ofβl) as
¯αl=f(l)
f(0), f(l) = cos/parenleftbiggl/L+η
1+η·π
2/parenrightbigg2
, (21)
whereL= 20 . From the relation between ¯αlandβl, we get
βl= 1−¯αl
¯αl−1. To prevent βlfrom being too close to 1, βlis
further clipped to be no larger than 0.999. ηis a small offset
to preventβlfrom being too small when close to l= 0, which
we set atη= 0.008in the following experiment.
F . Network Architecture
1) U-Net-like Structure: The architecture of the score ap-
proximator is detailed in Fig. 3. As Fig. 3 shows, it is
designed to have a fully convolutional structure similar to U-
Net [59] that takes the mel-spectrogram of input speech as
an input array and outputs an equally sized array. Note that
we have also tried other types of architectures, such as AE-
like bottleneck architectures without skip connections, b ut so
far we have experimentally conﬁrmed that the current U-Net-
like architecture works best. Here, the input and output of
each layer are vector sequences, where “c” and “l” denote

7
Fig. 3. Network architecture of the score approximator with a U-Net-like
fully convolutional structure. Here, ⊕represents array concatenation along
the channel direction. See V-F3 for details on how the noise- level and speaker
indices and the BNF sequence are incorporated into the netwo rk.
the channel number and the length of a vector sequence,
respectively. “Conv1d”, “GLU”, and “Deconv1d” denote 1D
convolution, gated linear unit (GLU) [60], and 1D transpose d
convolution layers, respectively. See below for the detail s of
GLUs. “k”, “c” and “s” denote the kernel size, output channel
number, and stride size of a convolution layer, respectivel y. All
the convolution weights are initilized using the Glorot nor mal
initializer [61] with gain 0.5 and reparameterized using we ight
normalization [62].
2) Gated Linear Unit: For non-linear activation functions,
we use GLUs [60]. The output of a GLU is deﬁned as
GLU(y) =y1⊙sigmoid(y2)whereyis the input, y1and
y2are equally sized arrays obtained by splitting yalong the
channel dimension, and sigmoid is a sigmoid gate function.
Like long short-term memory units, GLUs provide a linear
path for the gradients while retaining non-linear capabili ties,
thus reducing the vanishing gradient problem for deep archi -
tectures.
3) Noise-level, Speaker, and BNF Conditioning: The noise-
level and speaker indices are incorporated into each con-
volution layer in the score approximator by ﬁrst retrieving
embedding vectors from two learnable lookup tables accordi ng
to the speciﬁed indices, then repeating those vectors in the
time direction to the length compatible with the input of the
convolution layer, and ﬁnally concatenating the two repeat ed
vector sequences to the input along the channel direction.
In the version that incorporates a BNF sequence pinto the
network, pis ﬁrst fed into a strided convolution layer hwith
32 output channels with a stride size r, and then the output
is appended along the channel direction to the input of each
convolution layer with GLU. The stride size ris appropriately
chosen so that the length of the output of his compatible with
the input of the convolution layer with GLU.
VI. E XPERIMENTS
A. Dataset
To evaluate the performance of V oiceGrad, we conducted
speaker conversion experiments. For the experiments, we us edthe CMU ARCTIC database [63], which consists of recordings
of 18 speakers each reading the same 1,132 phonetically
balanced English sentences. For the training set and the tes t
set for a closed-set scenario, we used the utterances of two
female speakers, ‘clb’ and ‘slt’, and two male speakers, ‘bd l’
and ‘rms’. Thus, K= 4. We also used the utterances of two
male speakers, ‘jmk’ and ‘ksp’, and a female speaker, ‘lnh’,
as the test set for an open-set scenario. All the speech signa ls
were sampled at 16 kHz.
For each speaker, we ﬁrst split the 1,132 sentences into
1,000, 100, and 32 sentences and used the 32 sentences for
evaluation. To simulate a non-parallel training scenario, we
further divided the 1,000 sentences equally into four group s
and used the ﬁrst, second, third, and fourth groups for train ing
for speakers clb, bdl, slt, and rms, respectively, so as not t o use
the same sentences between different speakers. The trainin g
utterances of speakers clb, bdl, slt, and rms were about 12,
11, 11, and 14 minutes long in total, respectively. For the te st
set, we used the test utterances of speakers clb, bdl, slt, an d
rms for the closed-set scenario, and those of speakers jmk, k sp
and lnh for the open-set scenario.
B. Baseline Methods
We chose AE-based zero-shot VC method [21] (AutoVC),
PPG-based one-shot VC method [45] (PPG-VC), and our
previously proposed StarGAN-VC [29], [31] for comparison,
as these methods, in principle, are capable of addressing ma ny-
to-many scenarios but also any-to-many situations by lever -
aging non-parallel corpora. It should also be noted that the se
methods are employed in the systems submitted for VCC2020.
To run these methods, we used the source codes provided by
the respective authors [64]–[66]. For StarGAN-VC, we used
the Wasserstein distance as the training objective [31], [6 7]–
[69]. Although in the original paper [31], StarGAN-VC used
the mel-cepstral coefﬁcient (MCC) vector sequence of sourc e
speech as the feature to be converted, in this experiment it w as
modiﬁed to use the mel-spectrogram instead as the feature to
be converted and use HiFi-GAN [57] to generate waveforms
from the converted spectrograms [66], [70].
C. Model Setup
The score approximator network was trained using the
Adam optimizer [71] with random initialization, the learni ng
rate of 0.001, and the mini-batch size of 16. The starting
noise level index L′was set to 4 for Algorithm 3 and 11 for
Algorithm 4. The noise variances {σl}1≤l≤Land{βl}1≤l≤L
were set as described in V-E. The step size parameter εand
the iteration number Tin Algorithm 3 were set at 10−5and
32, respectively. Thus, the number of iterations required t o
complete the conversion was (21−4 + 1)×32 = 576 for
Algorithm 3 and 11for Algorithm 4. In Algorithm 4, νlis set
to√βl, following the guidance in reference [49].
D. Objective Evaluation Metrics
The test set for the above experiment consisted of speech
samples of each speaker reading the same sentences. We

8
TABLE I
COMPARISONS OF THE DSM AND DPM V ERSIONS
Speakers ↓MCD [dB] ↑LFC ↓CER [%] ↑pMOS
s t DSM DPM DSM DPM DSM DPM DSM DPM
bdl9.06±.128.25±.120.13±.050.38±.0514.613.73.21±.053.16±.05
clb slt7.24±.076.34±.070.49±.040.71±.037.17.23.56±.053.78±.06
rms8.45±.087.49±.080.33±.050.10±.0515.812.83.50±.053.00±.05
clb8.15±.117.08±.110.14±.050.17±.0420.722.82.76±.053.04±.06
bdl slt7.75±.107.18±.090.28±.040.41±.0416.316.93.16±.063.43±.05
rms8.36±.127.33±.140.29±.050.44±.0410.815.63.69±.043.77±.05
clb7.07±.086.24±.080.46±.040.69±.0310.711.63.23±.053.70±.05
slt bdl9.00±.117.93±.120.22±.050.55±.0415.010.13.09±.063.23±.05
rms8.56±.107.80±.110.28±.050.20±.0517.212.13.49±.052.92±.05
clb7.85±.087.05±.080.27±.050.07±.0519.114.82.90±.063.10±.07
rms bdl9.00±.137.95±.170.27±.060.29±.055.99.83.56±.043.72±.05
slt7.84±.127.51±.110.31±.050.08±.0517.013.73.29±.053.27±.05
All pairs 8.19±.047.35±.040.29±.010.34±.0214.213.43.29±.023.34±.02
TABLE II
EFFECT OF BNF C ONDITIONING IN DSM V ERSION
Speakers ↓MCD [dB] ↑LFC ↓CER [%] ↑pMOS
s t DSM DSM+BNF DSM DSM+BNF DSM DSM+BNF DSM DSM+BNF
bdl9.06±.127.44±.090.13±.050.53±.0314.62.53.21±.053.61±.05
clb slt7.24±.076.24±.060.49±.040.71±.027.12.33.56±.063.69±.05
rms8.45±.086.64±.070.33±.050.57±.0315.82.73.50±.053.71±.05
clb8.15±.116.72±.090.14±.050.44±.0420.73.32.76±.053.31±.06
bdl slt7.75±.106.88±.070.28±.040.61±.0316.33.13.16±.063.62±.05
rms8.36±.127.33±.140.29±.050.43±.0410.82.93.69±.043.81±.05
clb7.07±.086.14±.070.46±.040.68±.0310.71.83.23±.053.62±.05
slt bdl9.00±.117.38±.080.22±.050.57±.0315.02.23.09±.063.68±.05
rms8.56±.107.02±.110.28±.050.50±.0417.21.83.49±.053.65±.04
clb7.85±.086.45±.060.27±.050.54±.0319.11.12.90±.063.62±.06
rms bdl9.00±.137.89±.160.27±.060.42±.055.91.23.56±.043.80±.05
slt7.84±.126.74±.090.31±.050.55±.0417.01.43.29±.053.79±.05
All pairs 8.19±.046.91±.040.29±.010.55±.0114.22.23.29±.023.66±.02
TABLE III
EFFECT OF BNF C ONDITIONING IN DPM V ERSION
Speakers ↓MCD [dB] ↑LFC ↓CER [%] ↑pMOS
s t DPM DPM+BNF DPM DPM+BNF DPM DPM+BNF DPM DPM+BNF
bdl8.25±.126.51±.090.38±.050.52±.0313.72.43.16±.053.44±.05
clb slt6.34±.076.12±.060.71±.030.62±.037.22.33.78±.063.72±.05
rms7.49±.086.13±.060.10±.050.56±.0212.83.23.00±.053.59±.05
clb7.08±.115.89±.070.17±.040.57±.0322.83.43.04±.063.63±.05
bdl slt7.18±.096.20±.050.41±.040.63±.0216.93.03.43±.053.64±.05
rms7.33±.146.46±.120.44±.040.48±.0315.63.53.77±.053.55±.05
clb6.24±.085.79±.050.69±.030.59±.0311.61.53.70±.053.65±.05
slt bdl7.93±.126.54±.080.55±.040.53±.0310.12.23.23±.053.46±.06
rms7.80±.116.28±.090.20±.050.53±.0312.12.02.92±.053.60±.05
clb7.05±.086.02±.060.07±.050.52±.0314.81.23.10±.073.69±.05
rms bdl7.95±.176.91±.140.29±.050.44±.049.81.13.72±.053.51±.05
slt7.51±.116.44±.090.08±.050.55±.0313.71.23.27±.053.64±.05
All pairs 7.35±.046.27±.030.34±.020.55±.0113.42.23.34±.023.59±.01
evaluated the objective quality of the converted speech sam ples
using mel-cepstral distortion (MCD) [dB], log F0correlation
coefﬁcient (LFC), and character error rate (CER) [%]. We als o
evaluated the audio quality of the converted speech samples
with a mean opinion score (MOS) predictor. We refer to this
measure as the pseudo MOS (pMOS).
The utterance-level MCD was computed by averaging the
frame-level MCDs along the dynamic time warping (DTW)
path aligning the MCC vector sequences of the converted and
target speech. LFC was also computed based on this DTW
path. CER was evaluated using the wav2vec 2.0 model [72]
(the “Large LV-60K” architecture with an extra linear modul e),
pre-trained on 60,000 hours of unlabeled audio from Libri-
Light [73] dataset, and ﬁne-tuned on 960 hours of transcribe d
audio from LibriSpeech dataset [74]. To obtain the pMOS
of the converted speech, Saeki’s system [75] submitted to
the V oiceMOS challenge 2022 [76], which exhibited a strongcorrelation with human MOS ratings, was used as the MOS
predictor. The lower the value of MCD, the closer to 1 the
LFC, the closer to 0 the CER, and the closer to 5 the pMOS,
the better the performance.
E. Comparison of DSM and DPM Formulations
First, we compare the performance of the DSM and DPM
versions of V oiceGrad. Table I shows the average utterance-
level MCD and LFC, CER, and pMOS with 95% conﬁdence
intervals of the converted speech obtained with the DSM and
DPM versions of V oiceGrad. As the results show, the DPM
version performed slightly better than the DSM version for
all the metrics. Considering these results and the fact that
the DPM version required only 11 iterations to perform the
conversion, while the DSM version required 576 iterations,
the DPM version was conﬁrmed to be superior in our current
implementation. However, given that the CER for the ground

9
TABLE IV
MCD [ DB] C OMPARISONS WITH BASELINE METHODS
(a) Closed-set scenario
SpeakersStarGAN-VC AutoVC PPG-VC VoiceGrads t
bdl8.10±.119.48±.097.86±.106.51±.09
clb slt6.68±.078.63±.097.82±.096.12±.06
rms7.55±.079.81±.087.75±.086.13±.06
clb7.73±.119.15±.157.76±.115.89±.07
bdl slt7.85±.098.67±.088.00±.116.20±.05
rms8.06±.138.64±.108.30±.096.46±.12
clb6.52±.068.67±.077.47±.085.79±.05
slt bdl8.17±.099.17±.107.90±.106.54±.08
rms8.14±.109.82±.088.19±.096.28±.09
clb7.82±.089.05±.167.62±.086.02±.06
rms bdl8.93±.149.59±.098.41±.166.91±.14
slt8.51±.118.94±.108.20±.136.44±.09
All pairs 7.84±.049.13±.047.94±.036.27±.03
(b) Open-set scenario
SpeakersStarGAN-VC AutoVC PPG-VC VoiceGrads t
jmkclb8.44±.118.78±.167.86±.106.30±.08
bdl8.48±.129.10±.147.86±.106.76±.10
slt8.42±.088.33±.107.82±.096.40±.07
rms7.66±.118.44±.117.75±.086.57±.10
kspclb8.70±.109.14±.167.76±.116.67±.09
bdl9.64±.129.63±.097.76±.117.30±.12
slt8.68±.118.93±.098.00±.116.81±.07
rms8.18±.088.62±.108.30±.096.61±.07
lnhclb7.18±.119.23±.187.47±.086.15±.09
bdl8.16±.179.89±.117.90±.106.69±.13
slt7.40±.108.93±.127.90±.106.10±.05
rms8.08±.108.75±.138.19±.096.23±.07
All pairs 8.25±.048.98±.048.02±.046.55±.03
truth target speech was only 1.1%, we found that both version s
tended to produce speech with relatively low intelligibili ty in
terms of CER. In the following, we show that the idea of the
BNF conditioning can signiﬁcantly improve the CER of the
generated speech.
F . Effect of BNF Conditioning
We evaluated the effect of the BNF conditioning described
in V-D. Tables II and III display the performance of the DSM
and DPM versions of V oiceGrad with and without BNF con-
ditioning, where ‘DSM+BNF’ and ‘DPM+BNF’ refer to the
DSM and DPM versions with BNF conditioning, respevtively,
while ‘s’ and ‘t’ denote source and target, respectively. As
the results show, the incorporation of the BNF sequence into
the score approximator resulted in a signiﬁcant performanc e
improvement in terms of all of the metrics, especially in
CER. Signiﬁcant improvements were also observed in terms
of LFC, MCD, and pMOS, conﬁrming that BNF conditioning
contributes not only to intelligibility but also to the into nation,
speaker similarity, and audio quality of the generated spee ch.
This suggests that linguistic-related features can facili tate the
prediction of the target score function. Another ﬁnding was
that while the version without BNF conditioning performed
relatively less effectively in inter-gender conversions c ompared
to intra-gender conversions, BNF conditioning improved th e
conversions, making them less gender-dependent.
G. Comparison with Baseline Methods
Tables IV–VII show the MCD, LFC, CER, and pMOS
results of the proposed and baseline methods under the close d-
set and open-set conditions. Note that the proposed methodTABLE V
LFC C OMPARISONS WITH BASELINE METHODS
(a) Closed-set scenario
SpeakersStarGAN-VC AutoVC PPG-VC VoiceGrads t
bdl0.43±.050.23±.060.49±.040.52±.03
clb slt0.71±.030.37±.060.64±.040.62±.03
rms0.03±.040.25±.050.35±.050.56±.02
clb0.44±.050.02±.060.51±.040.57±.03
bdl slt0.56±.040.17±.060.55±.050.63±.02
rms0.12±.040.11±.050.28±.060.46±.03
clb0.69±.030.26±.060.62±.040.59±.03
slt bdl0.43±.050.19±.070.53±.040.53±.03
rms0.09±.040.20±.060.24±.060.53±.03
clb0.41±.040.03±.060.57±.030.52±.03
rms bdl0.18±.060.27±.050.43±.060.44±.04
slt0.25±.050.46±.040.52±.050.55±.03
All pairs 0.36±.020.21±.020.48±.010.55±.01
(b) Open-set scenario
SpeakersStarGAN-VC AutoVC PPG-VC VoiceGrads t
jmkclb0.25±.040.04±.060.56±.040.56±.03
bdl0.50±.050.10±.060.57±.040.50±.03
slt0.33±.050.15±.050.62±.040.61±.03
rms0.39±.050.05±.050.35±.050.53±.03
kspclb0.33±.05−0.01±.060.45±.050.51±.03
bdl0.08±.060.13±.060.34±.060.42±.03
slt0.28±.050.28±.050.42±.050.53±.03
rms0.17±.040.17±.050.26±.050.49±.03
lnhclb0.59±.03−0.05±.050.54±.040.55±.03
bdl0.31±.040.08±.060.39±.050.49±.03
slt0.59±.030.17±.050.57±.040.60±.03
rms0.10±.030.07±.050.33±.040.52±.03
All pairs 0.33±.020.10±.020.45±.010.53±.01
TABLE VI
CER [%] C OMPARISONS WITH BASELINE METHODS
(a) Closed-set scenario
SpeakersStarGAN-VC AutoVC PPG-VC VoiceGrads t
bdl 3.77 74.96 3.44 2.44
clb slt 1.59 73.89 3.03 2.35
rms 7.26 71.57 4.10 3.17
clb 5.25 71.56 3.96 3.40
bdl slt 5.69 72.06 4.08 2.96
rms 4.19 71.95 4.29 3.55
clb 1.18 71.70 2.19 1.47
slt bdl 5.33 73.68 2.11 2.21
rms 14.55 70.47 2.60 1.97
clb 4.95 71.89 1.15 1.17
rms bdl 1.32 74.68 1.34 1.09
slt 6.35 78.10 1.54 1.15
All pairs 5.12 73.04 2.82 2.24
(b) Open-set scenario
SpeakersStarGAN-VC AutoVC PPG-VC VoiceGrads t
jmkclb 14.48 72.71 3.01 3.51
bdl 3.53 73.31 2.71 3.48
slt 14.88 73.80 3.06 3.03
rms 3.26 72.03 3.40 3.59
kspclb 25.18 72.0510.64 12.45
bdl 10.78 75.28 11.43 13.36
slt 26.79 76.9612.67 13.93
rms 13.44 71.35 13.11 12.01
lnhclb 2.13 74.19 2.11 2.22
bdl 5.45 76.00 2.35 2.40
slt 2.19 75.40 1.94 2.16
rms 10.16 74.24 2.54 3.03
All pairs 11.02 73.94 5.75 6.26
here refers to the DPM version with BNF conditioning. For
reference, the MCD, LFC, CER, and pMOS of each source
speaker’s speech are shown in Table VIII.
As the results indicate, among the baseline methods, PPG-
VC excelled in nearly all metrics, particularly in CER and
pMOS, highlighting its capability to produce highly intell igi-

10
TABLE VII
PMOS C OMPARISONS WITH BASELINE METHODS
(a) Closed-set scenario
SpeakersStarGAN-VC AutoVC PPG-VC VoiceGrads t
bdl2.44±.051.24±.003.58±.053.44±.05
clb slt3.67±.061.32±.013.70±.063.72±.06
rms2.52±.051.30±.013.25±.063.59±.05
clb2.44±.061.26±.013.58±.053.63±.05
bdl slt2.53±.061.34±.013.68±.063.64±.05
rms2.85±.061.26±.013.38±.073.55±.05
clb3.75±.061.28±.013.54±.063.65±.05
slt bdl2.31±.051.25±.013.52±.053.46±.06
rms2.44±.061.31±.013.02±.073.60±.05
clb2.20±.061.25±.003.75±.053.69±.06
rms bdl2.99±.041.23±.003.79±.063.51±.05
slt2.27±.061.30±.013.73±.063.64±.05
All pairs 2.70±.031.28±.003.54±.023.59±.02
(b) Open-set scenario
SpeakersStarGAN-VC AutoVC PPG-VC VoiceGrads t
jmkclb2.10±.051.26±.013.70±.053.71±.05
bdl3.23±.051.25±.013.78±.053.55±.05
slt2.16±.061.40±.023.79±.063.66±.06
rms3.37±.051.26±.013.50±.053.69±.04
kspclb2.32±.061.26±.013.45±.053.59±.06
bdl2.82±.061.24±.003.56±.053.36±.05
slt2.46±.051.37±.013.48±.063.50±.05
rms2.43±.051.26±.013.13±.063.55±.05
lnhclb2.92±.061.28±.013.70±.053.77±.06
bdl2.48±.041.27±.013.73±.053.56±.05
slt2.77±.061.41±.023.72±.063.76±.05
rms2.44±.051.28±.013.36±.063.65±.05
All pairs 2.62±.021.30±.003.57±.023.61±.02
ble and high-quality speech. Remarkably, V oiceGrad slight ly
surpassed this top-performing baseline in terms of CER and
pMOS and notably outperformed it in MCD and LFC. This
suggests that the speech produced by V oiceGrad is as clear an d
natural as or even more so than that produced by PPG-VC,
while having features more similar to the target speaker. Th is
was also conﬁrmed by our subjective evaluation test, which
will be discussed in detail in the following section. StarGA N-
VC performed one step below PPG-VC and V oiceGrad in
overall performance. Nevertheless, the low CER conﬁrms
that StarGAN-VC’s intelligibility in the generated speech was
comparable to PPG-VC and V oiceGrad. AutoVC performed
worse than the other methods, even with the authors’ imple-
mentation as-is, likely due to its strong dependence on data set
domains for optimal architecture and hyperparameters. Whi le
some improvement might have been possible with speciﬁc
tuning for the CMU ARCTIC dataset, surpassing PPG-VC
appears challenging for AutoVC, as PPG-VC has consistently
shown to outperform AutoVC in both audio quality and
speaker similarity according to the results in VCC2020.
While all the tested methods are expected to handle any-to-
many conversions, it is crucial to evaluate their robustnes s to
speech input from unknown speakers. This can be conﬁrmed
in the differences in each method’s performance between
the closed-set and open-set conditions. Among the tested
methods, AutoVC and PPG-VC exhibited little difference
in performance between closed-set and open-set conditions .
In contrast, StarGAN-VC and V oiceGrad showed slight per-
formance degradation in MCD and CER in the open-set
condition. This indicates the high robustness of AutoVC and
PPG-VC in converting speech input from unknown speakersbut suggests issues with StarGAN-VC and V oiceGrad in this
regard. Nevertheless, V oiceGrad’s performance was the bes t
among the tested methods, except for CER. In terms of CER,
the converted speech from the source speaker ksp consistent ly
exhibited a relatively high error rate for all the methods.
This can be attributed to the fact that speaker ksp is an
Indian-accented English speaker and indicates challenges with
handling accented speech in all the methods.
As a reference, we also evaluated the MCD, LFC, CER,
and pMOS for the speech converted by Diff-VC [55] under
the same conditions as above. The results are shown in Table
IX. According to the results, while Diff-VC showed impressi ve
performance in terms of pMOS, V oiceGrad showed superior
performance in all the other metrics. Despite V oiceGrad bei ng
built on seemingly strong assumptions, as pointed out by the
authors of Diff-VC, its success hinges on a crucial factor: h ow
closely the distribution of the log mel-spectrogram differ ence
between source and target utterances resembles a Gaussian.
While its validity remains uncertain, the fact that V oiceGr ad
performs reasonably well may suggest that this assumption i s
not entirely inaccurate.
H. Real-Time Factor for Mel-Spectrogram Conversion
The real-time factor of the computation time required for
the mel-spectrogram conversion for each of the tested metho ds
is shown in Table X. As Table X shows, the DSM version of
V oiceGrad was considerably slower than the other methods,
while the DPM version was nearly as fast as PPG-VC. This
was due to the cosine-based noise variance scheduling, whic h
allowed the reverse diffusion process with considerably fe wer
steps to produce high-quality mel-spectrograms. We also fo und
that BNF conditioning did not have a signiﬁcant impact on
computation time. The fastest was StarGAN-VC, followed by
AutoVC. All algorithms were implemented in PyTorch and run
on a single Tesla V100 SXM2 GPU with a 32.0 GB memory
and an Intel(R) Xeon(R) Gold 5218 16-core CPU @ 2.30GHz.
I. Subjective Listening Tests
We conducted mean opinion score (MOS) tests to compare
the audio quality and speaker similarity of the converted
speech samples generated by the proposed and baseline meth-
ods. For the proposed method, we included samples of both the
DPM version with and without BNF conditioning. For these
tests, we used samples obtained under the close-set and open -
set conditions, as in VI-G. Sixteen listeners participated in both
tests. The tests were conducted online, where each particip ant
was asked to use a headphone in a quiet environment.
With the audio quality test, we included the real speech
samples of the target speakers as reference. Each listener w as
asked to evaluate the naturalness of each sample on a ﬁve-poi nt
scale by selecting 5: Excellent, 4: Good, 3: Fair, 2: Poor, or
1: Bad for each utterance. The scores with 95% conﬁdence
intervals are shown in Table XI. With the speaker similarity
test, each listener was given a converted speech sample and
a real speech sample of the corresponding target speaker and
asked to evaluate how likely they were produced by the same
speaker on a four-point scale by selecting 4: Same (sure), 3:

11
TABLE VIII
MCD [ DB], LFC, CER [%], AND P MOS OFSOURCE SPEECH
Speakers↓MCD [dB] ↑LFC ↓CER [%] ↑pMOSs t
bdl9.62±.110.30±.06
1.22 4.34 clb slt7.52±.070.63±.04
rms9.95±.090.30±.05
clb9.62±.110.30±.06
1.67 4.24 bdl slt9.69±.100.35±.06
rms9.58±.120.32±.06
clb7.52±.070.63±.04
0.76 4.36 slt bdl9.69±.100.35±.06
rms9.91±.080.30±.06
clb9.95±.090.30±.05
0.71 4.40 rms bdl9.58±.120.32±.06
slt9.91±.080.30±.06
jmkclb9.66±.090.11±.07
1.47 4.06bdl9.61±.100.46±.06
slt9.57±.080.25±.07
rms8.95±.100.37±.06
kspclb9.80±.100.16±.07
2.73 3.99bdl10.22±.120.10±.07
slt9.53±.100.21±.06
rms8.72±.080.28±.05
lnhclb8.68±.120.56±.04
1.28 4.24bdl8.50±.170.33±.06
slt8.87±.120.47±.06
rms9.13±.090.30±.05
TABLE IX
MCD [ DB], LFC, CER [%], AND P MOS O BTAINED WITH DIFF-VC [55]
(a) Closed-set scenario
Speakers↓MCD [dB] ↑LFC ↓CER [%] ↑pMOSs t
bdl7.75±.100.26±.05 2.81 3.86±.05
clb slt7.44±.080.30±.05 2.90 3.97±.05
rms7.75±.100.21±.04 3.39 3.77±.05
clb9.62±.110.30±.06 3.99 3.76±.06
bdl slt8.45±.160.22±.05 3.61 3.86±.06
rms8.83±.150.15±.05 4.02 3.67±.05
clb8.03±.120.25±.04 2.70 3.85±.06
slt bdl7.73±.100.28±.05 2.30 3.85±.05
rms8.18±.130.19±.04 3.06 3.71±.04
clb8.08±.100.26±.05 2.41 3.85±.05
rms bdl8.41±.150.20±.06 2.24 3.84±.05
slt7.79±.130.27±.05 2.58 3.96±.05
All pairs 8.01±.040.24±.01 3.00 3.83±.02
(b) Open-set scenario
Speakers↓MCD [dB] ↑LFC ↓CER [%] ↑pMOSs t
jmkclb8.11±.110.24±.05 4.66 3.96±.03
bdl8.03±.130.24±.06 4.35 3.97±.05
slt7.55±.090.31±.05 4.08 4.06±.04
rms8.16±.110.18±.06 4.65 3.87±.04
kspclb8.51±.100.22±.05 32.70 3.75±.05
bdl8.89±.150.19±.06 31.83 3.73±.05
slt8.26±.110.25±.05 33.19 3.97±.06
rms8.16±.080.13±.05 28.93 3.60±.05
lnhclb7.94±.120.31±.04 3.09 3.99±.05
bdl7.84±.160.24±.05 3.25 3.97±.05
slt7.19±.090.34±.05 2.52 4.03±.05
rms7.74±.110.24±.04 2.99 3.80±.04
All pairs 8.03±.040.24±.01 13.02 3.88±.01
TABLE X
REAL-TIMEFACTOR COMPARISONS
StarGAN-VC AutoVC PPG-VCV oiceGrad
DSM DPM DPM+BNF
0.0014 0.0057 0.0245 1.180 0.0221 0.0235
Same (not sure), 2: Different (not sure), or 1: Different (su re).
The scores with 95% conﬁdence intervals are shown in Table
XII.
Comparing the versions with and without BNF conditioningin V oiceGrad, the former was signiﬁcantly higher in both aud io
quality and speaker similarity, conﬁrming the effectivene ss of
BNF conditioning. Upon listening to the actual speech sam-
ples, it becomes clear that there is often noticeable phonem e
distortion in the speech converted without BNF conditionin g.
In contrast, the version with BNF conditioning demonstrate s
a signiﬁcant improvement in intelligibility. Among the bas e-
line methods, PPG-VC performed best in terms of audio
quality but fell short of StarGAN-VC in terms of speaker
similarity, and AutoVC performed worst in both tests. The

12
TABLE XI
RESULTS OF THE MOS T EST
Scenario Real StarGAN-VC AutoVC PPG-VCV oiceGrad
DPM DPM+BNF
Closed-set4.82±.122.66±.091.19±.053.65±.113.22±.103.77±.11
Open-set 2.61±.081.23±.053.70±.103.13±.103.81±.10
TABLE XII
RESULTS OF SPEAKER SIMILARITY TEST
Scenario StarGAN-VC AutoVC PPG-VCV oiceGrad
DPM DPM+BNF
Closed-set 2.56±.101.87±.091.88±.102.35±.143.58±.07
Open-set 2.02±.091.82±.091.92±.112.67±.103.67±.06
BNF-conditioned version of V oiceGrad performed better tha n
PPG-VC in audio quality and better than StarGAN-VC in
speaker similarity. These were consistent with the results of
the quantitative evaluation described in the previous sect ion.
The version without BNF conditioning performed reasonably
well, with better audio quality than StarGAN-VC, though not
as good as PPG-VC, and better speaker similarity than PPG-
VC, though slightly less than StarGAN-VC. However, it was
only in the closed-set condition that the version without BN F
conditioning was not as good as StarGAN-VC in terms of
speaker similarity, and in the open-set condition it perfor med
better than StarGAN-VC. Comparing the results of both tests
under closed-set and open-set conditions, all the methods
performed similarly in the closed and open set conditions, w ith
the exception of StarGAN-VC in the speaker similarity test.
It is noteworthy that while BNF conditioning demonstrated
only a modest effect of 0.2 to 0.4 in the quantitative evaluat ion
of pMOS, it yielded a more substantial impact of 0.5 to 0.7
in the subjective MOS evaluation. This divergence in result s
is anticipated, as the MOS predictor likely emphasizes the a s-
sessment of audio quality (naturalness as a speech waveform )
over intelligibility (the accuracy of phoneme pronunciati on).
Conversely, participants in the subjective listening test likely
took into account both audio quality and intelligibility wh en
evaluating the naturalness of the stimuli.
In summary, these results indicate from subjective listeni ng
tests that V oiceGrad (1) outperforms the tested baselines
in both audio quality and speaker similarity, (2) is greatly
improved by BNF conditioning, and (3) works as well for
unknown speaker input as for known speaker input.
Audio examples of these methods are provided at [77].
VII. C ONCLUSION
In this paper, we proposed V oiceGrad, a non-parallel any-to -
many VC method based upon the concepts of score matching,
Langevin dynamics, and DPMs: The idea involves training
a score approximator, a fully convolutional network with
a U-Net structure, to predict the gradient of the log den-
sity of the mel-spectrograms of multiple speakers. Once the
network is trained, it can be used to perform VC through
annealed Langevin dynamics or reverse diffusion process to
iteratively update the mel-spectrogram of input speech to
sound like a target speaker’s voice. Through objective and
subjective experiments, V oiceGrad has demonstrated super ior
performance to the tested baselines in terms of audio qualit yand speaker similarity under both closed-set and open-set
conditions. Additionally, we have found that the concept of
BNF conditioning signiﬁcantly enhances the intelligibili ty of
the generated speech.
APPENDIX
∇xllogq(xl) =Ex0[∇xllogq(xl|x0)]can be proved as
∇xllogq(xl) =∇xlq(xl)
q(xl)
=∇xl/integraltext
q(xl|x0)q(x0)dx0
q(xl)
=/integraltext
q(x0)∇xlq(xl|x0)dx0
q(xl)
=/integraltext
q(x0)∇xlexp(logq(xl|x0))dx0
q(xl)
=/integraltext
q(x0)q(xl|x0)∇xllogq(xl|x0)dx0
q(xl)
=/integraldisplay
q(x0|xl)∇xllogq(xl|x0)dx0
=Ex0∼q(x0|xl)[∇xllogq(xl|x0)], (22)
where the third line follows from the Leibniz integral rule,
the ﬁfth line follows from the chain rule, and the sixth line
follows from the Bayes rule.
REFERENCES
[1] A. Kain and M. W. Macon, “Spectral voice conversion for te xt-to-speech
synthesis,” in Proc. International Conference on Acoustics, Speech, and
Signal Processing (ICASSP) , 1998, pp. 285–288.
[2] A. B. Kain, J.-P. Hosom, X. Niu, J. P. van Santen, M. Fried- Oken, and
J. Staehely, “Improving the intelligibility of dysarthric speech,” Speech
Communication , vol. 49, no. 9, pp. 743–759, 2007.
[3] K. Nakamura, T. Toda, H. Saruwatari, and K. Shikano, “Spe aking-
aid systems using GMM-based voice conversion for electrola ryngeal
speech,” Speech Communication , vol. 54, no. 1, pp. 134–146, 2012.
[4] Z. Inanoglu and S. Young, “Data-driven emotion conversi on in spoken
English,” Speech Communication , vol. 51, no. 3, pp. 268–283, 2009.
[5] O. T¨ urk and M. Schr¨ oder, “Evaluation of expressive spe ech synthesis
with voice conversion and copy resynthesis techniques,” IEEE Transac-
tions on Audio, Speech, and Language Processing , vol. 18, no. 5, pp.
965–973, 2010.
[6] T. Toda, M. Nakagiri, and K. Shikano, “Statistical voice conversion
techniques for body-conducted unvoiced speech enhancemen t,”IEEE
Transactions on Audio, Speech, and Language Processing , vol. 20, no. 9,
pp. 2505–2517, 2012.
[7] P. Jax and P. Vary, “Artiﬁcial bandwidth extension of spe ech signals
using MMSE estimation based on a hidden Markov model,” in Proc.
International Conference on Acoustics, Speech, and Signal Processing
(ICASSP) , 2003, pp. 680–683.

13
[8] D. Felps, H. Bortfeld, and R. Gutierrez-Osuna, “Foreign accent conver-
sion in computer assisted pronunciation training,” Speech Communica-
tion, vol. 51, no. 10, pp. 920–932, 2009.
[9] D. P. Kingma and M. Welling, “Auto-encoding variational Bayes,” in
Proc. International Conference on Learning Representatio ns (ICLR) ,
2014.
[10] D. P. Kingma, D. J. Rezendey, S. Mohamedy, and M. Welling , “Semi-
supervised learning with deep generative models,” in Adv. Neural
Information Processing Systems (NIPS) , 2014, pp. 3581–3589.
[11] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. War de-Farley,
S. Ozair, A. Courville, and Y . Bengio, “Generative adversar ial nets,” in
Adv. Neural Information Processing Systems (NIPS) , 2014, pp. 2672–
2680.
[12] L. Dinh, D. Krueger, and Y . Bengio, “NICE: Non-linear in dependent
components estimation,” in Proc. International Conference on Learning
Representations (ICLR) , 2015.
[13] L. Dinh, J. Sohl-Dickstein, and S. Bengio, “Density est imation using real
NVP,” in Proc. International Conference on Learning Representatio ns
(ICLR) , 2017.
[14] D. P. Kingma and P. Dhariwal, “Glow: Generative ﬂow with invertible
1x1 convolutions,” in Advances in Neural Information Processing Sys-
tems 31 , 2018, pp. 10 215–10 224.
[15] C.-C. Hsu, H.-T. Hwang, Y .-C. Wu, Y . Tsao, and H.-M. Wang , “V oice
conversion from non-parallel corpora using variational au to-encoder,”
inProc. Asia-Paciﬁc Signal and Information Processing Assoc iation
Annual Summit and Conference (APSIPA ASC) , 2016, pp. 1–6.
[16] C.-C. Hsu, H.-T. Hwang, Y .-C. Wu, Y . Tsao, and H.-M. Wang ,
“V oice conversion from unaligned corpora using variationa l autoen-
coding Wasserstein generative adversarial networks,” in Proc. Annual
Conference of the International Speech Communication Asso ciation
(Interspeech) , 2017, pp. 3364–3368.
[17] A. van den Oord and O. Vinyals, “Neural discrete represe ntation
learning,” in Adv. Neural Information Processing Systems (NIPS) , 2017,
pp. 6309–6318.
[18] W.-C. Huang, H.-T. Hwang, Y .-H. Peng, Y . Tsao, and H.-M. Wang,
“V oice conversion based on cross-domain features using var iational
auto encoders,” in Proc. International Symposium on Chinese Spoken
Language Processing (ISCSLP) , 2018, pp. 165–169.
[19] Y . Saito, Y . Ijima, K. Nishida, and S. Takamichi, “Non-p arallel voice
conversion using variational autoencoders conditioned by phonetic
posteriorgrams and d-vectors,” in Proc. International Conference on
Acoustics, Speech, and Signal Processing (ICASSP) , 2018, pp. 5274–
5278.
[20] H. Kameoka, T. Kaneko, K. Tanaka, and N. Hojo, “ACV AE-VC :
Non-parallel voice conversion with auxiliary classiﬁer va riational au-
toencoder,” IEEE/ACM Transactions on Audio, Speech, and Language
Processing , vol. 27, no. 9, pp. 1432–1443, 2019.
[21] K. Qian, Y . Zhang, S. Chang, X. Yang, and M. Hasegawa-Joh nson,
“AutoVC: Zero-shot voice style transfer with only autoenco der loss,”
inProc. International Conference on Machine Learning (ICML) , 2019,
pp. 5210–5219.
[22] L. Wan, Q. Wang, A. Papir, and I. L. Moreno, “Generalized end-to-
end loss for speaker veriﬁcation,” in Proc. 2018 IEEE International
Conference on Acoustics, Speech and Signal Processing (ICA SSP) , 2018,
pp. 4879–4883.
[23] T. Kaneko and H. Kameoka, “CycleGAN-VC: Non-parallel v oice con-
version using cycle-consistent adversarial networks,” in Proc. European
Signal Processing Conference (EUSIPCO) , 2018, pp. 2100–2104.
[24] T. Kaneko, H. Kameoka, K. Tanaka, and N. Hojo, “CycleGAN -VC2:
Improved cyclegan-based non-parallel voice conversion,” inProc. In-
ternational Conference on Acoustics, Speech, and Signal Pr ocessing
(ICASSP) , 2019, pp. 6820–6824.
[25] J.-Y . Zhu, T. Park, P. Isola, and A. A. Efros, “Unpaired i mage-to-
image translation using cycle-consistent adversarial net works,” in Proc.
International Conference on Computer Vision (ICCV) , 2017, pp. 2223–
2232.
[26] T. Kim, M. Cha, H. Kim, J. K. Lee, and J. Kim, “Learning to d iscover
cross-domain relations with generative adversarial netwo rks,” in Proc.
International Conference on Machine Learning (ICML) , 2017, pp. 1857–
1865.
[27] Z. Yi, H. Zhang, P. Tan, and M. Gong, “DualGAN: Unsupervi sed
dual learning for image-to-image translation,” in Proc. International
Conference on Computer Vision (ICCV) , 2017, pp. 2849–2857.
[28] P. L. Tobing, Y .-C. Wu, T. Hayashi, K. Kobayashi, and T. T oda,
“Non-parallel voice conversion with cyclic variational au toencoder,” in
Proc. Annual Conference of the International Speech Commun ication
Association (Interspeech) , 2019, pp. 674–678.[29] H. Kameoka, T. Kaneko, K. Tanaka, and N. Hojo, “StarGAN- VC: Non-
parallel many-to-many voice conversion using star generat ive adversarial
networks,” in Proc. IEEE Spoken Language Technology Workshop (SLT) ,
2018, pp. 266–273.
[30] T. Kaneko, H. Kameoka, K. Tanaka, and N. Hojo, “StarGAN- VC2:
Rethinking conditional methods for stargan-based voice co nversion,” in
Proc. Annual Conference of the International Speech Commun ication
Association (Interspeech) , 2019, pp. 679–683.
[31] H. Kameoka, T. Kaneko, K. Tanaka, and N. Hojo, “Nonparal lel voice
conversion with augmented classiﬁer star generative adver sarial net-
works,” IEEE/ACM Transactions on Audio, Speech, and Language
Processing , vol. 28, pp. 2982–2995, 2020.
[32] Y . Choi, M. Choi, M. Kim, J.-W. Ha, S. Kim, and J. Choo, “St arGAN:
Uniﬁed generative adversarial networks for multi-domain i mage-to-
image translation,” arXiv:1711.09020 [cs.CV] , Nov. 2017.
[33] J. Serr` a, S. Pascual, and C. Segura, “Blow: a single-sc ale hy-
perconditioned ﬂow for non-parallel raw-audio voice conve rsion,”
arXiv:1906.00794 [cs.LG] , Jun. 2019.
[34] K. Tanaka, H. Kameoka, T. Kaneko, and N. Hojo, “AttS2S-V C:
Sequence-to-sequence voice conversion with attention and context
preservation mechanisms,” in Proc. International Conference on Acous-
tics, Speech, and Signal Processing (ICASSP) , 2019, pp. 6805–6809.
[35] H. Kameoka, K. Tanaka, D. Kwa´ sny, and N. Hojo, “ConvS2S -VC:
Fully convolutional sequence-to-sequence voice conversi on,” IEEE/ACM
Transactions on Audio, Speech, and Language Processing , vol. 28, pp.
1849–1863, 2020.
[36] W.-C. Huang, T. Hayashi, Y .-C. Wu, H. Kameoka, and T. Tod a,
“V oice transformer network: Sequence-to-sequence voice c onversion
using transformer with text-to-speech pretraining,” arXiv:1912.06813
[eess.AS] , Dec. 2019.
[37] H. Kameoka, W.-C. Huang, K. Tanaka, T. Kaneko, N. Hojo, a nd
T. Toda, “Many-to-many voice transformer network,” arXiv:2005.08445
[eess.AS] , 2020.
[38] H. Zheng, W. Cai, T. Zhou, S. Zhang, and M. Li, “Text-inde pendent
voice conversion using deep neural network based phonetic l evel fea-
tures,” in Proc. International Conference on Pattern Recognition (IC PR),
2016, pp. 2872–2877.
[39] L. Sun, K. Li, H. Wang, S. Kang, and H. Meng, “Phonetic pos -
teriorgrams for many-to-one voice conversion without para llel data
training,” in 2016 IEEE International Conference on Multimedia and
Expo (ICME) , 2016, pp. 1–6.
[40] H. Miyoshi, Y . Saito, S. Takamichi, and H. Saruwatari, “ V oice con-
version using sequence-to-sequence learning of context po sterior prob-
abilities,” in Proc. Annual Conference of the International Speech
Communication Association (Interspeech) , 2017, pp. 1268–1272.
[41] L.-J. Liu, Z.-H. Ling, Y . Jiang, M. Zhou, and L.-R. Dai, “ WaveNet
vocoder with limited training data for voice conversion,” i nProc. Annual
Conference of the International Speech Communication Asso ciation
(Interspeech) , 2018, pp. 1983–1987.
[42] S. Liu, J. Zhong, L. Sun, X. Wu, X. Liu, and H. Meng, “V oice conversion
across arbitrary speakers based on a single target-speaker utterance,” in
Proc. Annual Conference of the International Speech Commun ication
Association (Interspeech) , 2018, pp. 496–500.
[43] J.-X. Zhang, Z.-H. Ling, and L.-R. Dai, “Non-parallel s equence-to-
sequence voice conversion with disentangled linguistic an d speaker
representations,” arXiv:1906.10508 [eess.AS] , 2019.
[44] J.-X. Zhang, Z.-H. Ling, and L.-R. Dai, “Recognition-s ynthesis based
non-parallel voice conversion with adversarial learning, ” inProc. Annual
Conference of the International Speech Communication Asso ciation
(Interspeech) , 2020, pp. 771–775.
[45] S. Liu, Y . Cao, D. Wang, X. Wu, X. Liu, and H. Meng, “Any-to -many
voice conversion with location-relative sequence-to-seq uence modeling,”
IEEE/ACM Transactions on Audio, Speech, and Language Proce ssing ,
vol. 29, pp. 1717–1728, 2021.
[46] Y . Zhao, W.-C. Huang, X. Tian, J. Yamagishi, R. K. Das, T. Kinnunen,
Z. Ling, and T. Toda, “V oice Conversion Challenge 2020: Intr a-lingual
semi-parallel and cross-lingual voice conversion,” in Proc. Joint work-
shop for the Blizzard Challenge and Voice Conversion Challe nge, 2020,
pp. 80–98.
[47] Y . Song and S. Ermon, “Generative modeling by estimatin g gradients
of the data distribution,” in Adv. Neural Information Processing Systems
(NeurIPS) , 2019, pp. 11 918–11 930.
[48] Y . Song and S. Ermon, “Improved techniques for training score-based
generative models,” arXiv:2006.09011 [cs.LG] , 2020.
[49] J. Ho, A. Jain, and P. Abbeel, “Denoising diffusion prob abilistic models,”
inAdvances in Neural Information Processing Systems (NeurIP S),
vol. 33, 2020, pp. 6840–6851.

14
[50] A. Q. Nichol and P. Dhariwal, “Improved denoising diffu sion proba-
bilistic models,” in Proceedings of the 38th International Conference on
Machine Learning , vol. 139, 2021, pp. 8162–8171.
[51] A. Hyv¨ arinen, “Estimation of non-normalized statist ical models using
score matching,” Journal of Machine Learning Research , vol. 6, pp.
695–709, 2005.
[52] P. Vincent, “A connection between score matching and de noising au-
toencoders.” Neural Computation , vol. 23, no. 7, pp. 1661–1674, 2011.
[53] N. Chen, Y . Zhang, H. Zen, R. J. Weiss, M. Norouzi, and
W. Chan, “WaveGrad: Estimating gradients for waveform gene ration,”
arXiv:2009.00713 [eess.AS] , 2020.
[54] S. Liu, Y . Cao, D. Su, and H. Meng, “DiffSVC: A diffusion p roba-
bilistic model for singing voice conversion,” in IEEE Automatic Speech
Recognition and Understanding Workshop (ASRU) , 2021, pp. 741–748.
[55] V . Popov, I. V ovk, V . Gogoryan, T. Sadekova, M. Kudinov, and J. Wei,
“Diffusion-based voice conversion with fast maximum likel ihood sam-
pling scheme,” in Proc. International Conference on Learning Repre-
sentations (ICLR) , 2022.
[56] H. Kameoka, T. Kaneko, K. Tanaka, N. Hojo, and S. Seki, “V oiceGrad:
Non-parallel any-to-many voice conversion with annealed l angevin
dynamics,” arXiv:2010.02977 [cs.SD] , 2020.
[57] J. Kong, J. Kim, and J. Bae, “HiFi-GAN: Generative adver sarial net-
works for efﬁcient and high ﬁdelity speech synthesis,” in Advances in
Neural Information Processing Systems (NeurIPS) , 2020, pp. 17 022–
17 033.
[58] S. Kim, T. Hori, and S. Watanabe, “Joint CTC-attention b ased end-
to-end speech recognition using multi-task learning,” in Proc. IEEE
International Conference on Acoustics, Speech and Signal P rocessing
(ICASSP) , 2017, pp. 4835–4839.
[59] O. Ronneberger, P. Fischer, and T. Brox, “U-Net: Convol utional net-
works for biomedical image segmentation,” arXiv:1505.04597 [cs.CV] ,
2015.
[60] Y . N. Dauphin, A. Fan, M. Auli, and D. Grangier, “Languag e modeling
with gated convolutional networks,” in Proc. International Conference
on Machine Learning (ICML) , 2017, pp. 933–941.
[61] X. Glorot and Y . Bengio, “Understanding the difﬁculty o f training
deep feedforward neural networks,” in Proc. the 13th International
Conference on Artiﬁcial Intelligence and Statistics , 2010, pp. 249–256.
[62] T. Salimans and D. P. Kingma, “Weight normalization: A s imple
reparameterization to accelerate training of deep neural n etworks,”
inAdvances in Neural Information Processing Systems (NIPS) ,
D. Lee, M. Sugiyama, U. Luxburg, I. Guyon, and R. Garnett,
Eds., vol. 29. Curran Associates, Inc., 2016. [Online]. Ava ilable:
https://proceedings.neurips.cc/paper/2016/ﬁle/ed265 bc903a5a097f61d3ec064d96d2e-Paper.pdf
[63] J. Kominek and A. W. Black, “The CMU Arctic speech databa ses,” in
Proc. ISCA Speech Synthesis Workshop (SSW) , 2004, pp. 223–224.
[64] https://github.com/auspicious3000/autovc.
[65] https://github.com/liusongxiang/ppg-vc.
[66] https://github.com/kamepong/StarGAN-VC.
[67] M. Arjovsky and L. Bottou, “Towards principled methods for training
generative adversarial networks,” in Proc. International Conference on
Learning Representations (ICLR) , 2017.
[68] I. Gulrajani, F. Ahmed, M. Arjovsky, V . Dumoulin, and A. Courville,
“Improved training of Wasserstein GANs,” in Adv. Neural Information
Processing Systems (NIPS) , 2017, pp. 5769–5779.
[69] Y . Choi, M. Choi, M. Kim, J.-W. Ha, S. Kim, and J. Choo, “St arGAN:
Uniﬁed generative adversarial networks for multi-domain i mage-to-
image translation,” in Proc. IEEE Conference on Computer Vision and
Pattern Recognition , vol. 1711, 2018.
[70] http://www.kecl.ntt.co.jp/people/kameoka.hiroka zu/Demos/stargan-vc2/.
[71] D. Kingma and J. Ba, “Adam: A method for stochastic optim ization,”
inProc. International Conference on Learning Representatio ns (ICLR) ,
2015.
[72] A. Baevski, Y . Zhou, A. Mohamed, and M. Auli, “wav2vec 2. 0: A
framework for self-supervised learning of speech represen tations,” in
Advances in Neural Information Processing Systems , vol. 33, 2020, pp.
12 449–12 460.
[73] J. Kahn, M. Rivi` ere, W. Zheng, E. Kharitonov, Q. Xu, P. M azar´ e,
J. Karadayi, V . Liptchinsky, R. Collobert, C. Fuegen, T. Lik homanenko,
G. Synnaeve, A. Joulin, A. Mohamed, and E. Dupoux, “Libri-Li ght:
A benchmark for ASR with limited or no supervision,” in Proc.
International Conference on Acoustics, Speech and Signal P rocessing
(ICASSP) , 2020, pp. 7669–7673.
[74] V . Panayotov, G. Chen, D. Povey, and S. Khudanpur, “Libr ispeech:
An ASR corpus based on public domain audio books,” in Proc. IEEE
International Conference on Acoustics, Speech and Signal P rocessing
(ICASSP) , 2015, pp. 5206–5210.[75] T. Saeki, D. Xin, W. Nakata, T. Koriyama, S. Takamichi, a nd
H. Saruwatari, “UTMOS: UTokyo-SaruLab system for V oiceMOS Chal-
lenge 2022,” in Proc. Annual Conference of the International Speech
Communication Association (Interspeech) , vol. 4521-4525, 2022.
[76] W. C. Huang, E. Cooper, Y . Tsao, H.-M. Wang, T. Toda, and J . Yam-
agishi, “The V oiceMOS Challenge 2022,” in Proc. Annual Conference
of the International Speech Communication Association (In terspeech) ,
2022, pp. 4536–4540.
[77] http://www.kecl.ntt.co.jp/people/kameoka.hiroka zu/Demos/voicegrad2/.

