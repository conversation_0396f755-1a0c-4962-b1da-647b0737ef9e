#!/usr/bin/env python3

import sys
import os
sys.path.append('server')

from baseline import PaperToExam
import json

def test_toeic_generation():
    """Test TOEIC Part 5 generation"""
    processor = PaperToExam()
    processor.markdown_content = '''
# Business Communication in the Digital Age

In today's rapidly evolving business environment, effective communication has become more crucial than ever. Companies are increasingly relying on digital platforms to facilitate internal and external communications. Email remains the primary mode of business correspondence, with professionals sending an average of 40 emails per day.

The rise of remote work has further emphasized the importance of clear, concise communication. Video conferencing tools like Zoom and Microsoft Teams have become essential for maintaining team collaboration. However, the challenge lies in ensuring that all team members remain engaged and productive during virtual meetings.

Furthermore, social media platforms are now integral to business marketing strategies. Companies use LinkedIn, Twitter, and Facebook to reach their target audiences and build brand awareness. The key to successful social media marketing is understanding the preferences and behaviors of different demographic groups.

Project management software has also revolutionized how teams coordinate their efforts. Tools like Asana, Trello, and Monday.com enable teams to track progress, assign tasks, and meet deadlines more efficiently. These platforms provide transparency and accountability, which are essential for successful project completion.

In conclusion, businesses that adapt to new communication technologies and train their employees accordingly will have a competitive advantage in the marketplace.
'''

    try:
        print("Testing TOEIC Part 5 generation...")
        result = processor.generate_exam('toeic', '600-700', '5')
        print('TOEIC Part 5 Generation Result:')
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # Validate result
        if 'part_number' in result and result['part_number'] == 5:
            print("\n✅ Part number is correct")
        else:
            print("\n❌ Part number is incorrect")
            
        if 'questions' in result and len(result['questions']) == 30:
            print("✅ Question count is correct (30)")
        else:
            print(f"❌ Question count is incorrect: {len(result.get('questions', []))}")
            
        if 'reading_passages' in result and len(result['reading_passages']) == 0:
            print("✅ No passages for Part 5 (correct)")
        else:
            print(f"❌ Should have no passages for Part 5, found: {len(result.get('reading_passages', []))}")
            
        return True
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_toeic_generation()
